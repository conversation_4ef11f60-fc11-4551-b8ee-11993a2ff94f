codeunit 60010 GasCylinderBarcodeHandlerGAS
{
    [IntegrationEvent(false, false)]
    local procedure OnBeforeInsertLine(var CylinderEntryExitLine: Record "Cylinder Entry/Exit Line GAS"; Barcode: Text[50]; HeaderRecRef: RecordRef)
    begin
    end;

    [IntegrationEvent(false, false)]
    local procedure OnAfterInsertLine(var CylinderEntryExitLine: Record "Cylinder Entry/Exit Line GAS"; Barcode: Text[50]; HeaderRecRef: RecordRef)
    begin
    end;

    procedure HandleBarcodeScan(Barcode: Text[50]; HeaderRecRef: RecordRef): Boolean
    var
        CylinderEntryExitLine: Record "Cylinder Entry/Exit Line GAS";
        SerialNoInfo: Record "Serial No. Information";
        ItemRec: Record Item;
        DefaultLocation: Record Location;
        DocNo: Code[20];
        CylinderItemNo: Code[20];
        GasItemNo: Code[20];
        GasQuantity: Decimal;
    begin
        // Validate barcode
        if StrLen(DelChr(Barcode, '=', ' ')) = 0 then
            Error(ErrorBarcodeIsEmptyLbl);

        // Validate header reference
        if HeaderRecRef.Number() = 0 then
            Error(ErrorHeaderRecRefNotInitLbl);

        // Get Document No. from HeaderRecRef
        DocNo := Format(HeaderRecRef.Field(1).Value());
        if StrLen(DocNo) = 0 then
            Error(ErrorDocNoNotFoundLbl);



        // Prevent duplicate insertion for the document
        CylinderEntryExitLine.SetLoadFields("Document No.", "Cylinder Serial No.");
        CylinderEntryExitLine.SetRange("Document No.", DocNo);
        CylinderEntryExitLine.SetRange("Cylinder Serial No.", Barcode);
        if not CylinderEntryExitLine.IsEmpty() then
            Error(ErrorCylinderAlreadyInDocLbl, Barcode, DocNo);

        // Validate barcode against Serial No. Information
        SerialNoInfo.SetLoadFields("Serial No.", "Item No.", "Gas Item No. GAS", "Gas Quantity GAS");
        SerialNoInfo.SetRange("Serial No.", Barcode);
        if not SerialNoInfo.FindFirst() then
            Error(ErrorCylinderNotExistLbl, Barcode);

        // Get Cylinder and Gas Item Numbers
        CylinderItemNo := SerialNoInfo."Item No.";
        GasItemNo := SerialNoInfo."Gas Item No. GAS";
        SerialNoInfo.CalcFields("Gas Quantity GAS");
        GasQuantity := SerialNoInfo."Gas Quantity GAS";

        // Get Cylinder Item Description
        if not ItemRec.Get(CylinderItemNo) then
            Error(ErrorCylinderItemNotFoundLbl, CylinderItemNo);

        // Validate default location exists
        DefaultLocation.SetRange("Default Gas Fill Location GAS", true);
        if DefaultLocation.IsEmpty() then
            Error(ErrorNoDefaultLocationLbl);

        // Prepare new line
        Clear(CylinderEntryExitLine);
        CylinderEntryExitLine.Init();
        CylinderEntryExitLine."Document No." := DocNo;
        // "Line No." will be assigned in OnInsert trigger
        CylinderEntryExitLine."Read Cylinder Barcode" := Barcode;
        CylinderEntryExitLine."Cylinder Serial No." := Barcode;
        CylinderEntryExitLine."Cylinder Item No." := CylinderItemNo;
        CylinderEntryExitLine."Cylinder Item Description" := ItemRec.Description;
        CylinderEntryExitLine."Gas Item No." := GasItemNo;
        if (GasItemNo <> '') and ItemRec.Get(GasItemNo) then
            CylinderEntryExitLine."Gas Item Description" := ItemRec.Description;
        CylinderEntryExitLine."Gas Quantity" := GasQuantity;
        // No "Location Code" field on CylinderEntryExitLine; skip assignment.

        // Raise OnBeforeInsertLine event
        OnBeforeInsertLine(CylinderEntryExitLine, Barcode, HeaderRecRef);

        // Insert line with error handling
        if not CylinderEntryExitLine.Insert(true) then
            Error(ErrorInsertFailedLbl, Barcode);

        // Raise OnAfterInsertLine event
        OnAfterInsertLine(CylinderEntryExitLine, Barcode, HeaderRecRef);

        exit(true);
    end;
    // Error labels
    var
        ErrorBarcodeIsEmptyLbl: Label 'Barcode is empty.';
        ErrorHeaderRecRefNotInitLbl: Label 'Header record reference is not initialized.';
        ErrorDocNoNotFoundLbl: Label 'Unable to retrieve Document No. from header.';
        ErrorCylinderNotExistLbl: Label 'Cylinder with serial number %1 does not exist.', Comment = '%1 = Cylinder serial number/barcode';
        ErrorCylinderAlreadyInDocLbl: Label 'Cylinder %1 is already included in document %2.', Comment = '%1 = Cylinder serial number/barcode, %2 = Document number';
        ErrorCylinderItemNotFoundLbl: Label 'Cylinder item %1 not found.', Comment = '%1 = Cylinder item number';
        ErrorNoDefaultLocationLbl: Label 'No default gas filling location has been set up.';
        ErrorInsertFailedLbl: Label 'Failed to insert cylinder entry/exit line for barcode %1.', Comment = '%1 = Cylinder serial number/barcode';
}