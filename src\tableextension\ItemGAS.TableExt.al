tableextension 60002 "Item GAS" extends Item
{
    fields
    {
        field(60001; "Gas GAS"; Boolean)
        {
            Caption = 'Gas from Item Category';
            FieldClass = FlowField;
            Editable = false;
            CalcFormula = lookup("Item Category"."Gas GAS");
            ToolTip = 'Displays the gas value from the associated item category.';
        }
        // field(60000; "Gas Item No. GAS"; Code[20])
        // {
        //     Caption = 'Gas Item No.';
        //     DataClassification = CustomerContent;
        //     TableRelation = Item;
        //     ToolTip = 'Specifies the gas item number associated with this item.';
        // }
    }
}