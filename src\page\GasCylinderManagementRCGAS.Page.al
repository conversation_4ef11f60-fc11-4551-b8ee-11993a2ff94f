page 60006 "Gas Cylinder Management RC GAS"
{
    PageType = RoleCenter;
    Caption = 'Gas Cylinder Management Role Center';
    ApplicationArea = All;

    layout
    {
        area(RoleCenter)
        {
            part(GasCylinderActivitiesPart; "Gas Cylinder Activities GAS")
            {
                Caption = 'Gas Cylinder Activities';
            }
        }
    }

    actions
    {
        area(Processing) // Corrected Casing
        {
            // Add other processing actions here if needed
        }
        area(Reporting)
        {
            // Add reports here
        }
        area(Embedding)
        {
            // Add embedded pages here
            action(GasItems)
            {
                ApplicationArea = All;
                Caption = 'Gas Items';
                RunObject = page "Item List";
                RunPageLink = "Gas GAS" = const(true);
                Image = Item;
                ToolTip = 'View and manage gas cylinder items.';
            }
        }
        area(Creation)
        {
            // Add creation actions here
        }
    }
}
