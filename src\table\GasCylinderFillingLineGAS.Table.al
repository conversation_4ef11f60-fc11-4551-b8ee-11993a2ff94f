table 60004 "Gas Cylinder Filling Line GAS"
{
    Caption = 'Gas Cylinder Filling Line';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            TableRelation = "Gas Cyld. Filling Header GAS";
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the line number.';
        }
        field(3; "Cylinder Serial No."; Code[50])
        {
            Caption = 'Cylinder Serial No.';
            TableRelation = "Serial No. Information"."Serial No.";
            ToolTip = 'Specifies the serial number of the gas cylinder.';
            trigger OnValidate()
            var
                GasCylinderFillingMgt: Codeunit "Gas Cylinder Filling Mgt. GAS";
            begin
                if "Cylinder Serial No." = '' then begin
                    "Cylinder Item No." := '';
                    "Cylinder Item Description" := '';
                    Quantity := 0;
                    "Gas Item No." := '';
                    "Gas Item Description" := '';
                    // Update cylinder count when serial number is cleared
                    // if GasCylinderFillingHeader.Get("Document No.") then
                    //     //GasCylinderFillingHeader.UpdateNoOfCylinders();
                    exit;
                end;

                // Check if this cylinder already has a line in this document
                CheckDuplicateCylinderLine();

                // Populate defaults and validate location (main logic moved to AddCylinderToDocument)
                GasCylinderFillingMgt.PopulateLineDefaultsAndValidateLocation(Rec);

                // Update cylinder count when a new serial number is set
                // if GasCylinderFillingHeader.Get("Document No.") then
                //     GasCylinderFillingHeader.UpdateNoOfCylinders();
            end;
        }
        field(4; "Cylinder Item No."; Code[20])
        {
            Caption = 'Cylinder Item No.';
            TableRelation = Item;
            ToolTip = 'Specifies the item number of the gas cylinder.';
            trigger OnValidate()
            var
                ItemRec: Record Item;
            begin
                if "Cylinder Item No." = '' then begin
                    "Cylinder Item Description" := '';
                    exit;
                end;

                ItemRec.Get("Cylinder Item No.");
                "Cylinder Item Description" := ItemRec.Description;


                // Validate filling quantity based on cylinder type
                ValidateFillingQuantity();
            end;
        }
        field(5; "Cylinder Item Description"; Text[100])
        {
            Caption = 'Cylinder Item Description';
            Editable = false;
            ToolTip = 'Specifies the description of the gas cylinder item.';
        }
        field(8; Quantity; Decimal)
        {
            Caption = 'Quantity';
            DecimalPlaces = 0 : 5;
            MinValue = 0;
            ToolTip = 'Specifies the quantity of gas to fill in the cylinder.';
            trigger OnValidate()
            begin
                if (Quantity <= 0) and ("Cylinder Serial No." <> '') then
                    Error(QuantityMustBePositiveErr);
            end;
        }
        field(9; "Gas Item No."; Code[20])
        {
            Caption = 'Gas Item No.';
            TableRelation = Item;
            ToolTip = 'Specifies the gas item number to be filled into the cylinder.';
            trigger OnValidate()
            var
                ItemRec: Record Item;
                MixedGasManagement: Codeunit "Mixed Gas Management GAS";
            begin
                if "Gas Item No." = '' then begin
                    "Gas Item Description" := '';
                    "Mixed Gas" := false;
                    exit;
                end;

                ItemRec.Get("Gas Item No.");
                "Gas Item Description" := ItemRec.Description;

                // Check if this gas item has an Assembly BOM
                "Mixed Gas" := CheckForAssemblyBOM("Gas Item No.");
                if "Mixed Gas" then
                    MixedGasManagement.CreateAssemblyOrderFromGasCylinderFillingLine(Rec);
            end;
        }
        field(10; "Gas Item Description"; Text[100])
        {
            Caption = 'Gas Item Description';
            Editable = false;
            ToolTip = 'Specifies the description of the gas item.';
        }
        field(11; "From Cylinder Serial No."; Code[50])
        {
            Caption = 'From Cylinder Serial No.';
            TableRelation = "Serial No. Information"."Serial No." where("Gas Item No. GAS" = field("Gas Item No."));
            ToolTip = 'Specifies the serial number of the first cylinder in the batch.';
        }
        field(12; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            TableRelation = Location;
            ToolTip = 'Specifies the location where the filled cylinders will be stored.';
            trigger OnValidate()
            var
                Location: Record Location;
            begin
                if "Location Code" = '' then begin
                    Location.SetRange("Default Gas Fill Location GAS", true);
                    if Location.FindFirst() then
                        "Location Code" := Location.Code;
                end;
            end;
        }
        field(13; "Assembly Order No."; Code[20])
        {
            Caption = 'Assembly Order No.';
            ToolTip = 'Specifies the assembly order number created for this filling line.';
        }
        field(14; "Mixed Gas"; Boolean)
        {
            Caption = 'Mixed Gas';
            Editable = false;
            ToolTip = 'Specifies whether the gas item has an Assembly BOM and is considered a mixed gas.';
        }
    }

    keys
    {
        key(Key1; "Document No.", "Line No.")
        {
            Clustered = true;
        }
        key(Key2; "Document No.", "Cylinder Serial No.")
        {
        }
        key(Key3; "Cylinder Serial No.")
        {
        }
    }

    trigger OnInsert()
    var
        GasCylinderFillingHeader: Record "Gas Cyld. Filling Header GAS";
    begin
        if GasCylinderFillingHeader.Get("Document No.") then
            GasCylinderFillingHeader.TestField(Status, GasCylinderFillingHeader.Status::Open);
        // GasCylinderFillingHeader.UpdateNoOfCylinders();
    end;

    trigger OnModify()
    var
        GasCylinderFillingHeader: Record "Gas Cyld. Filling Header GAS";
    begin
        if GasCylinderFillingHeader.Get("Document No.") then begin
            GasCylinderFillingHeader.TestField(Status, GasCylinderFillingHeader.Status::Open);
            // // Update cylinder count if Cylinder Serial No. changed
            if "Cylinder Serial No." <> xRec."Cylinder Serial No." then
                GasCylinderFillingHeader.UpdateNoOfCylinders();
        end;
    end;

    trigger OnDelete()
    var
        GasCylinderFillingHeader: Record "Gas Cyld. Filling Header GAS";
        AssemblyHeader: Record "Assembly Header";
    begin
        if GasCylinderFillingHeader.Get("Document No.") then
            GasCylinderFillingHeader.TestField(Status, GasCylinderFillingHeader.Status::Open);

        // Delete associated Assembly Order if exists
        if "Assembly Order No." <> '' then begin
            AssemblyHeader.SetLoadFields("No.", Status);
            if AssemblyHeader.Get(AssemblyHeader."Document Type"::Order, "Assembly Order No.") then
                if AssemblyHeader.Status = AssemblyHeader.Status::Open then
                    AssemblyHeader.Delete(true);
        end;
    end;

    local procedure CheckDuplicateCylinderLine()
    var
        GasCylinderFillingLine: Record "Gas Cylinder Filling Line GAS";
    begin
        GasCylinderFillingLine.Reset();
        GasCylinderFillingLine.SetRange("Document No.", "Document No.");
        GasCylinderFillingLine.SetRange("Cylinder Serial No.", "Cylinder Serial No.");
        GasCylinderFillingLine.SetFilter("Line No.", '<>%1', "Line No.");
        if not GasCylinderFillingLine.IsEmpty() then
            Error(DuplicateCylinderErr, "Cylinder Serial No.", "Document No.");
    end;

    local procedure ValidateFillingQuantity()
    var
        ItemRec: Record Item;
    begin
        if "Cylinder Item No." <> '' then
            if ItemRec.Get("Cylinder Item No.") then
                Quantity := ItemRec."Unit Volume"
            else
                Quantity := 0;
    end;

    local procedure CheckForAssemblyBOM(ItemNo: Code[20]): Boolean
    var
        BomComponent: Record "BOM Component";
    begin
        BomComponent.Reset();
        BomComponent.SetRange("Parent Item No.", ItemNo);
        BomComponent.SetRange(Type, BomComponent.Type::Item);
        exit(not BomComponent.IsEmpty());
    end;

    var
        DuplicateCylinderErr: Label 'Cylinder %1 is already included in document %2.', Comment = '%1 = Serial Number, %2 = Document No.';
        QuantityMustBePositiveErr: Label 'Quantity must be greater than 0.';
}
