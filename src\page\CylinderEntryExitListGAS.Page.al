page 60018 "Cylinder Entry/Exit List GAS"
{
    PageType = List;
    SourceTable = "Cylinder Entry/Exit Header GAS";
    Caption = 'Cylinder Entry/Exits';
    UsageCategory = Lists;
    ApplicationArea = All;
    Extensible = false;

    layout
    {
        area(Content)
        {
            repeater(Group)
            {
                field("No."; Rec."No.")
                {
                    Caption = 'No.';
                    ToolTip = 'Specifies the number of the document.';
                }
                field("Document Date"; Rec."Document Date")
                {
                    Caption = 'Document Date';
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    Caption = 'Posting Date';
                }
                field("Customer No."; Rec."Customer No.")
                {
                    Caption = 'Customer No.';
                }
                field("Customer Name"; Rec."Customer Name")
                {
                    Caption = 'Customer Name';
                }
                field("Ship-to Code"; Rec."Ship-to Code")
                {
                    Caption = 'Ship-to Code';
                }
                field("Ship-to Name"; Rec."Ship-to Name")
                {
                    Caption = 'Ship-to Name';
                }
                field("Entry/Exit Type"; Rec."Entry/Exit Type")
                {
                    Caption = 'Entry/Exit Type';
                }
                field(Barcode; Rec."Barcode")
                {
                    Caption = 'Barcode';
                }
            }
        }
    }
    actions
    {
        area(Processing)
        {
            action(Card)
            {
                Caption = 'Open Card';
                Image = EditLines;
                RunObject = page "Cylinder Entry/Exit Header GAS";
                RunPageMode = View;
                ApplicationArea = All;
                ToolTip = 'Opens the Cylinder Entry/Exit Card for the selected document.';
            }
        }
    }
}