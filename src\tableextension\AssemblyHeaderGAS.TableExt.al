tableextension 60010 "Assembly Header GAS" extends "Assembly Header"
{
    fields
    {
        field(60000; "Gas Filling Document No. GAS"; Code[20])
        {
            Caption = 'Gas Filling Document No.';
            ToolTip = 'Specifies the gas filling document number associated with this assembly order.';
        }
        field(60001; "Gas Filling Doc. Line No. GAS"; Integer)
        {
            Caption = 'Gas Filling Document Line No.';
            ToolTip = 'Specifies the line number of the gas filling document associated with this assembly order.';
        }
    }
    trigger OnAfterDelete()
    var
        GasCylinderFillingLine: Record "Gas Cylinder Filling Line GAS";
    begin
        // Delete related gas cylinder filling lines when assembly header is deleted
        if GasCylinderFillingLine.Get(Rec."Gas Filling Document No. GAS", Rec."Gas Filling Doc. Line No. GAS") then begin
            GasCylinderFillingLine."Assembly Order No." := '';
            GasCylinderFillingLine.Modify(true);
        end;
    end;
}