codeunit 60002 "Gas Cylinder Filling Mgt. GAS"
{
    TableNo = "Gas Cyld. Filling Header GAS";
    procedure ScanCylinder(var GasCylinderFillingHeader: Record "Gas Cyld. Filling Header GAS")
    var
        BarcodePage: Page "Gas Cylinder Barcode GAS";
        SerialNo: Code[50];
    begin
        // Check if the document is open
        if GasCylinderFillingHeader.Status <> GasCylinderFillingHeader.Status::Open then
            Error(DocumentMustBeOpenErr);


        // Show barcode scanning dialog
        BarcodePage.LookupMode(true);
        if BarcodePage.RunModal() = Action::LookupOK then begin
            SerialNo := BarcodePage.GetBarcodeValue();
            if SerialNo <> '' then
                AddCylinderToDocument(GasCylinderFillingHeader, SerialNo);
        end;
    end;

    local procedure AddCylinderToDocument(GasCylinderFillingHeader: Record "Gas Cyld. Filling Header GAS"; SerialNo: Code[50])
    var
        GasCylinderFillingLine: Record "Gas Cylinder Filling Line GAS";
        SerialNoInformation: Record "Serial No. Information";
        //BomComponent: Record "BOM Component";
        Item: Record Item;
        DefaultLocation: Record Location;
        GasDefaultTank: Record "GasDefTankPerLoc GAS";
        LineNo: Integer;
        GasItemNo: Code[20];
        CylinderItemNo: Code[20];
        LocationCode: Code[10];
        CylinderVolume: Decimal;
    //ComponentCount: Integer;
    //HasBOM: Boolean;
    begin
        // Check if cylinder exists
        SerialNoInformation.Reset();
        SerialNoInformation.SetRange("Serial No.", SerialNo);
        if not SerialNoInformation.FindFirst() then
            Error(CylinderNotFoundErr, SerialNo);

        // Check if cylinder is already in the document
        GasCylinderFillingLine.Reset();
        GasCylinderFillingLine.SetRange("Document No.", GasCylinderFillingHeader."No.");
        GasCylinderFillingLine.SetRange("Cylinder Serial No.", SerialNo);
        if not GasCylinderFillingLine.IsEmpty() then
            Error(CylinderAlreadyInDocumentErr, SerialNo, GasCylinderFillingHeader."No.");

        // Get common data for all lines
        GasItemNo := SerialNoInformation."Gas Item No. GAS";
        CylinderItemNo := SerialNoInformation."Item No.";

        // Get cylinder quantity
        if Item.Get(CylinderItemNo) then
            CylinderVolume := Item."Unit Volume"
        else
            CylinderVolume := 0;

        // Get default location
        DefaultLocation.Reset();
        DefaultLocation.SetRange("Default Gas Fill Location GAS", true);
        if DefaultLocation.FindFirst() then
            LocationCode := DefaultLocation.Code
        else
            Error(DefaultLocationNotFoundErrLbl);

        // Create single line for regular gas item
        // Get next line number
        GasCylinderFillingLine.Reset();
        GasCylinderFillingLine.SetRange("Document No.", GasCylinderFillingHeader."No.");
        if GasCylinderFillingLine.FindLast() then
            LineNo := GasCylinderFillingLine."Line No." + 10000
        else
            LineNo := 10000;

        // Create new line
        GasCylinderFillingLine.Init();
        GasCylinderFillingLine."Document No." := GasCylinderFillingHeader."No.";
        GasCylinderFillingLine."Line No." := LineNo;
        GasCylinderFillingLine.Insert(true);

        // Set cylinder fields
        GasCylinderFillingLine."Cylinder Serial No." := SerialNo;
        GasCylinderFillingLine."Cylinder Item No." := CylinderItemNo;
        if Item.Get(CylinderItemNo) then
            GasCylinderFillingLine."Cylinder Item Description" := Item.Description;
        GasCylinderFillingLine."Location Code" := LocationCode;
        GasCylinderFillingLine.Quantity := CylinderVolume;

        // Set gas item
        GasCylinderFillingLine.Validate("Gas Item No.", GasItemNo);

        // Set default tank for this gas
        GasDefaultTank.Reset();
        GasDefaultTank.SetRange("Location Code", LocationCode);
        GasDefaultTank.SetRange("Gas Item No.", GasItemNo);
        if GasDefaultTank.FindFirst() then
            GasCylinderFillingLine.Validate("From Cylinder Serial No.", GasDefaultTank."Default Tank Serial No.");

        GasCylinderFillingLine.Modify(true);

        Message(CylinderAddedMsg, SerialNo);
    end;

    procedure ReleaseDocument(var GasCylinderFillingHeader: Record "Gas Cyld. Filling Header GAS")
    var
        GasCylinderFillingLine: Record "Gas Cylinder Filling Line GAS";
    begin
        // Check if document is already released or posted
        if GasCylinderFillingHeader.Status <> GasCylinderFillingHeader.Status::Open then
            Error(DocumentMustBeOpenErr);

        // Check if required fields are filled
        GasCylinderFillingHeader.TestField("No.");
        // GasCylinderFillingHeader.TestField("To Location Code");

        // Check if there are lines in the document
        GasCylinderFillingLine.Reset();
        GasCylinderFillingLine.SetRange("Document No.", GasCylinderFillingHeader."No.");
        if GasCylinderFillingLine.IsEmpty() then
            Error(NoLinesErr);

        // Update status
        GasCylinderFillingHeader.Status := GasCylinderFillingHeader.Status::Released;
        GasCylinderFillingHeader.Modify(true);

        Message(DocumentReleasedMsg);
    end;

    procedure ReopenDocument(var GasCylinderFillingHeader: Record "Gas Cyld. Filling Header GAS")
    begin
        // Check if document is released (not posted)
        if GasCylinderFillingHeader.Status <> GasCylinderFillingHeader.Status::Released then
            Error(DocumentMustBeReleasedErr);

        // Update status
        GasCylinderFillingHeader.Status := GasCylinderFillingHeader.Status::Open;
        GasCylinderFillingHeader.Modify(true);

        Message(DocumentReopenedMsg);
    end;

    procedure PostDocument(var GasCylinderFillingHeader: Record "Gas Cyld. Filling Header GAS")
    var
        GasCylinderFillingLine: Record "Gas Cylinder Filling Line GAS";
        Window: Dialog;
        LineCounter: Integer;
        TotalLines: Integer;
        ItemJnlBatch: Code[10];
        ItemJnlTemplate: Code[10];
    begin
        // Check if document is released
        if GasCylinderFillingHeader.Status <> GasCylinderFillingHeader.Status::Released then
            Error(DocumentMustBeReleasedErr);

        // Check required fields
        GasCylinderFillingHeader.TestField("No.");

        // Check if there are lines in the document
        GasCylinderFillingLine.Reset();
        GasCylinderFillingLine.SetRange("Document No.", GasCylinderFillingHeader."No.");
        TotalLines := GasCylinderFillingLine.Count();
        if TotalLines = 0 then
            Error(NoLinesErr);

        // Get setup values for item journal template and batch
        GasCylMgtSetup.GetSetup();
        ItemJnlTemplate := GasCylMgtSetup."Cylinder Filling Template Name";
        ItemJnlBatch := GasCylMgtSetup."Cylinder Filling Batch Name";

        // Clear existing item journal entries in this batch
        ClearItemJournal(ItemJnlTemplate, ItemJnlBatch);

        Window.Open(PostingDialogTxt);

        // Process each cylinder line
        GasCylinderFillingLine.Reset();
        GasCylinderFillingLine.SetRange("Document No.", GasCylinderFillingHeader."No.");
        LineCounter := 0;

        if GasCylinderFillingLine.FindSet() then
            repeat
                LineCounter += 1;
                Window.Update(1, Round(LineCounter / TotalLines * 10000, 1));

                if GasCylinderFillingLine."Assembly Order No." <> '' then
                    // Post the assembly order if it exists
                    PostAssemblyOrder(GasCylinderFillingLine."Assembly Order No.")
                else
                    // Create item journal line for regular gas
                    CreateItemJournalLine(
                        ItemJnlTemplate,
                        ItemJnlBatch,
                        GasCylinderFillingLine."Gas Item No.",
                        GasCylinderFillingLine."Location Code",
                        GasCylinderFillingLine."Location Code",
                        GasCylinderFillingLine."Cylinder Serial No.",
                        GasCylinderFillingLine.Quantity,
                        GasCylinderFillingHeader."Posting Date",
                        GasCylinderFillingLine."From Cylinder Serial No."
                    );
            until GasCylinderFillingLine.Next() = 0;

        // Post item journal entries (if any were created)
        PostItemJournal(ItemJnlTemplate, ItemJnlBatch);

        Window.Update(1, 10000);

        // Update status
        GasCylinderFillingHeader.Status := GasCylinderFillingHeader.Status::Posted;
        GasCylinderFillingHeader.Modify(true);

        Window.Close();
        Message(PostingCompletedMsg);
    end;

    // Add a new procedure to post assembly orders
    local procedure PostAssemblyOrder(AssemblyOrderNo: Code[20])
    var
        AssemblyHeader: Record "Assembly Header";
        AssemblyPost: Codeunit "Assembly-Post";
    begin
        AssemblyHeader.SetRange("Document Type", AssemblyHeader."Document Type"::Order);
        AssemblyHeader.SetRange("No.", AssemblyOrderNo);
        if AssemblyHeader.FindFirst() then begin
            if AssemblyHeader.Status <> AssemblyHeader.Status::Released then begin
                AssemblyHeader.Validate(Status, AssemblyHeader.Status::Released);
                AssemblyHeader.Modify(true);
            end;

            AssemblyPost.Run(AssemblyHeader);
        end else
            Error(AssemblyOrderNotFoundErr, AssemblyOrderNo);
    end;

    local procedure CreateItemJournalLine(
                                            ItemJnlTemplate: Code[10];
                                            ItemJnlBatch: Code[10];
                                            ItemNo: Code[20];
                                            FromLocation: Code[10];
                                            ToLocation: Code[10];
                                            SerialNo: Code[50];
                                            Quantity: Decimal;
                                            PostingDate: Date;
                                            OldSerialNo: Code[50])
    var
        ItemJnlLine: Record "Item Journal Line";
        ItemJnlLineLast: Record "Item Journal Line";
        NextLineNo: Integer;
    begin
        // Get setup
        GasCylMgtSetup.GetSetup();
        ItemJnlTemplate := GasCylMgtSetup."Cylinder Filling Template Name";
        ItemJnlBatch := GasCylMgtSetup."Cylinder Filling Batch Name";

        // Find last line for setupnewline and determine next Line No.
        ItemJnlLine.Reset();
        ItemJnlLine.SetRange("Journal Template Name", ItemJnlTemplate);
        ItemJnlLine.SetRange("Journal Batch Name", ItemJnlBatch);
        if ItemJnlLine.FindLast() then begin
            ItemJnlLineLast := ItemJnlLine;
            NextLineNo := ItemJnlLine."Line No." + 10000;
        end else
            NextLineNo := 10000;

        // Use SetUpNewLine to let system assign document no, etc.
        ItemJnlLine.Init();
        ItemJnlLine.Validate("Journal Template Name", ItemJnlTemplate);
        ItemJnlLine.Validate("Journal Batch Name", ItemJnlBatch);
        ItemJnlLine.SetUpNewLine(ItemJnlLineLast);

        ItemJnlLine.Validate("Line No.", NextLineNo);
        ItemJnlLine.Validate("Entry Type", ItemJnlLine."Entry Type"::Transfer);
        ItemJnlLine.Validate("Item No.", ItemNo);
        ItemJnlLine.Validate("Posting Date", PostingDate);
        ItemJnlLine.Validate("Location Code", FromLocation);
        ItemJnlLine.Validate("New Location Code", ToLocation);
        ItemJnlLine.Validate(Quantity, Quantity);
        ItemJnlLine.Insert(true);

        // Assign package tracking to relate cylinder serial number with gas
        AssignOldAndNewPackageNo(ItemJnlLine, OldSerialNo, SerialNo);
    end;

    local procedure ClearItemJournal(ItemJnlTemplate: Code[10]; ItemJnlBatch: Code[10])
    var
        ItemJnlLine: Record "Item Journal Line";
    begin
        ItemJnlLine.SetRange("Journal Template Name", ItemJnlTemplate);
        ItemJnlLine.SetRange("Journal Batch Name", ItemJnlBatch);
        if ItemJnlLine.FindSet(true) then
            repeat
                ItemJnlLine.Delete(true);
            until ItemJnlLine.Next() = 0;
    end;

    local procedure PostItemJournal(ItemJnlTemplate: Code[10]; ItemJnlBatch: Code[10])
    var
        ItemJnlLine: Record "Item Journal Line";
        ItemJnlPostBatch: Codeunit "Item Jnl.-Post Batch";
    begin
        ItemJnlLine.SetRange("Journal Template Name", ItemJnlTemplate);
        ItemJnlLine.SetRange("Journal Batch Name", ItemJnlBatch);
        if ItemJnlLine.FindFirst() then
            ItemJnlPostBatch.Run(ItemJnlLine);
    end;

    local procedure AssignOldAndNewPackageNo(var ItemJournalLine: Record "Item Journal Line"; OldPackageNo: Code[50]; NewPackageNo: Code[50])
    var
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        ItemJnlLineReserve: Codeunit "Item Jnl. Line-Reserve";
        ItemTrackingLines: Page "Item Tracking Lines";
    begin
        ItemJnlLineReserve.InitFromItemJnlLine(TempSourceTrackingSpecification, ItemJournalLine);

        TempTrackingSpecification.Init();
        TempTrackingSpecification."Package No." := OldPackageNo;
        TempTrackingSpecification."New Package No." := NewPackageNo;

        TempTrackingSpecification.SetQuantities(ItemJournalLine."Quantity (Base)",
                                                ItemJournalLine.Quantity,
                                                ItemJournalLine."Quantity (Base)",
                                                0,
                                                0,
                                                0,
                                                0);
        TempTrackingSpecification.Insert(false);
        ItemTrackingLines.SetRunMode(Enum::"Item Tracking Run Mode"::Reclass);
        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, ItemJournalLine."Posting Date", TempTrackingSpecification);
    end;

    // [EventSubscriber(ObjectType::Page, Page::"Item Tracking Lines", OnAfterCreateReservEntryFor, '', false, false)]
    // local procedure "Item Tracking Lines_OnAfterCreateReservEntryFor"(var OldTrackingSpecification: Record "Tracking Specification"; var NewTrackingSpecification: Record "Tracking Specification"; var CreateReservEntry: Codeunit "Create Reserv. Entry")
    // begin
    //     if OldTrackingSpecification."New Package No." <> '' then
    //         CreateReservEntry.SetNewTrackingFromNewTrackingSpecification(OldTrackingSpecification);
    // end;

    procedure PopulateLineDefaultsAndValidateLocation(var CurrentGasCylFillingLine: Record "Gas Cylinder Filling Line GAS")
    var
        GasCylinderFillingHeader: Record "Gas Cyld. Filling Header GAS";
        DefaultLocation: Record Location;
        GasDefaultTank: Record "GasDefTankPerLoc GAS";
        CylinderSerialNoInfo: Record "Serial No. Information";
        SerialNoInfo: Record "Serial No. Information";
    //MixedGasMgt: Codeunit "Mixed Gas Management GAS"; // Added for new functionality
    begin
        // Part 1: Header Check
        if GasCylinderFillingHeader.Get(CurrentGasCylFillingLine."Document No.") then
            GasCylinderFillingHeader.TestField(Status, GasCylinderFillingHeader.Status::Open)
        else
            Error(DocumentHeaderNotFoundErrLbl, CurrentGasCylFillingLine."Document No."); // Generic error if header not found

        // Part 2: Populate Location Code (only if currently blank on the line)
        if CurrentGasCylFillingLine."Location Code" = '' then begin
            DefaultLocation.Reset();
            DefaultLocation.SetRange("Default Gas Fill Location GAS", true);
            if DefaultLocation.FindFirst() then
                CurrentGasCylFillingLine.Validate("Location Code", DefaultLocation.Code)
            else
                Error(DefaultLocationNotFoundErrLbl);
        end;

        // Populate Gas Item No. from Serial No. Information if not already set or if different
        if CurrentGasCylFillingLine."Cylinder Serial No." <> '' then begin
            SerialNoInfo.SetRange("Serial No.", CurrentGasCylFillingLine."Cylinder Serial No.");
            if SerialNoInfo.FindFirst() then
                if (CurrentGasCylFillingLine."Gas Item No." = '') or (CurrentGasCylFillingLine."Gas Item No." <> SerialNoInfo."Gas Item No. GAS") then
                    CurrentGasCylFillingLine.Validate("Gas Item No.", SerialNoInfo."Gas Item No. GAS");
        end;

        // Part 3: Populate "From Cylinder Serial No." (if Gas Item No. is specified)
        if CurrentGasCylFillingLine."Gas Item No." <> '' then begin
            GasDefaultTank.Reset();
            GasDefaultTank.SetRange("Location Code", CurrentGasCylFillingLine."Location Code");
            GasDefaultTank.SetRange("Gas Item No.", CurrentGasCylFillingLine."Gas Item No.");
            if GasDefaultTank.FindFirst() then
                CurrentGasCylFillingLine.Validate("From Cylinder Serial No.", GasDefaultTank."Default Tank Serial No.")
            else
                Message(DefaultTankNotFoundErrLbl, CurrentGasCylFillingLine."Location Code", CurrentGasCylFillingLine."Gas Item No.");
        end;

        // Part 4: Test Cylinder Location (if Cylinder Serial No. and Item No. are specified)
        if (CurrentGasCylFillingLine."Cylinder Serial No." <> '') and (CurrentGasCylFillingLine."Cylinder Item No." <> '') then
            if CylinderSerialNoInfo.Get(CurrentGasCylFillingLine."Cylinder Item No.", '', CurrentGasCylFillingLine."Cylinder Serial No.") then begin
                CylinderSerialNoInfo.CalcFields("Location Code GAS");
                if CylinderSerialNoInfo."Location Code GAS" <> CurrentGasCylFillingLine."Location Code" then
                    Error(CylinderLocationMismatchErrLbl, CurrentGasCylFillingLine."Cylinder Serial No.", CurrentGasCylFillingLine."Location Code", CylinderSerialNoInfo."Location Code GAS", CurrentGasCylFillingLine."Cylinder Item No.");
            end else
                Error(CylinderSerialNoNotFoundForLocCheckErrLbl, CurrentGasCylFillingLine."Cylinder Serial No.", CurrentGasCylFillingLine."Cylinder Item No.");
    end;

    [EventSubscriber(ObjectType::Table, Database::"Gas Cylinder Filling Line GAS", OnAfterDeleteEvent, '', false, false)]
    local procedure OnAfterDeleteEvent_GasCylinderFillingLineGAS(var Rec: Record "Gas Cylinder Filling Line GAS"; RunTrigger: Boolean)
    var
        GasCyldFillingHeader: Record "Gas Cyld. Filling Header GAS";
    begin
        GasCyldFillingHeader.Get(Rec."Document No.");
        GasCyldFillingHeader.UpdateNoOfCylinders();
    end;

    [EventSubscriber(ObjectType::Table, Database::"Gas Cylinder Filling Line GAS", OnAfterInsertEvent, '', false, false)]
    local procedure OnAfterInsertEvent_GasCylinderFillingLineGAS(var Rec: Record "Gas Cylinder Filling Line GAS"; RunTrigger: Boolean)
    var
        GasCyldFillingHeader: Record "Gas Cyld. Filling Header GAS";
    begin
        if GasCyldFillingHeader.Get(Rec."Document No.") then
            GasCyldFillingHeader.UpdateNoOfCylinders();
    end;

    var
        GasCylMgtSetup: Record "Gas Cyl. Mgt. Setup GAS";
        CylinderNotFoundErr: Label 'Cylinder with serial number %1 does not exist.', Comment = '%1 = Serial Number';
        CylinderAlreadyInDocumentErr: Label 'Cylinder %1 is already included in document %2.', Comment = '%1 = Serial Number, %2 = Document No.';
        DocumentMustBeOpenErr: Label 'Document must be in Open status.';
        DocumentMustBeReleasedErr: Label 'Document must be in Released status.';
        NoLinesErr: Label 'There are no lines in this document.';
        PostingDialogTxt: Label 'Posting Gas Cylinder Filling Document... @1@@@@@';
        CylinderAddedMsg: Label 'Cylinder %1 added to the document.', Comment = '%1 = Serial Number';
        DocumentReleasedMsg: Label 'Document has been released.';
        DocumentReopenedMsg: Label 'Document has been reopened.';
        PostingCompletedMsg: Label 'Posting completed successfully.';
        // Added Labels for PopulateLineDefaultsAndValidateLocation procedure
        DefaultLocationNotFoundErrLbl: Label 'No default gas filling location has been set up.';
        DefaultTankNotFoundErrLbl: Label 'No default tank is defined for gas item %2 in location %1.', Comment = '%1 = Location, %2 = Gas Item No.';
        CylinderLocationMismatchErrLbl: Label 'Cylinder %1 (Item %4) is not in location %2. It is currently in location %3.', Comment = '%1 = Cylinder Serial No., %2 = Line Location, %3 = Actual Cylinder Location, %4 = Cylinder Item No.';
        CylinderSerialNoNotFoundForLocCheckErrLbl: Label 'Cylinder with serial number %1 (Item %2) could not be retrieved for location check.', Comment = '%1 = Serial No, %2 = Item No.';
        DocumentHeaderNotFoundErrLbl: Label 'Internal error: Document header %1 not found for line operation.', Comment = '%1 = Document No.';
        AssemblyOrderNotFoundErr: Label 'Assembly Order %1 not found.', Comment = '%1 = Assembly Order No.';
}
