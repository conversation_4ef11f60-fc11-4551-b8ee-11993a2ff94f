page 60011 "Gas Cylinder Filling Card GAS"
{
    PageType = Document;
    SourceTable = "Gas Cyld. Filling Header GAS";
    Caption = 'Gas Cylinder Filling';
    UsageCategory = None;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';

                field("No."; Rec."No.")
                {
                    ApplicationArea = All;
                    ToolTip = 'Specifies the document number.';
                    Editable = DocumentEditable;
                }
                field("Document Date"; Rec."Document Date")
                {
                    ApplicationArea = All;
                    Editable = DocumentEditable;
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    ApplicationArea = All;
                    Editable = DocumentEditable;
                }
                field(Status; Rec.Status)
                {
                    ApplicationArea = All;
                    StyleExpr = StatusStyleTxt;
                    Editable = false;
                }
                field("Total Quantity"; Rec."Total Quantity")
                {
                    ApplicationArea = All;
                }
                field("No. of Cylinders"; Rec."No. of Cylinders")
                {
                    ApplicationArea = All;
                }
                field("User ID"; Rec."User ID")
                {
                    ApplicationArea = All;
                    Editable = false;
                }
            }

            part(Lines; "Gas Cyld. Filling Subform GAS")
            {
                ApplicationArea = All;
                SubPageLink = "Document No." = field("No.");
                UpdatePropagation = Both;
                Editable = DocumentEditable;
            }
        }
        area(FactBoxes)
        {
            systempart(Links; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(Notes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

    actions
    {
        area(Navigation)
        {
            action("Cylinder &Information")
            {
                ApplicationArea = All;
                Caption = 'Cylinder &Information';
                Image = Item;
                RunObject = page "Serial No. Information Card";
                RunPageLink = "Serial No." = field("No.");
                ShortcutKey = 'Ctrl+F7';
                ToolTip = 'View or edit detailed cylinder information.';
                Enabled = CylinderInfoActionEnabled;
            }
        }

        area(Processing)
        {
            action("&Scan Cylinder")
            {
                ApplicationArea = All;
                Caption = '&Scan Cylinder';
                Image = BarCode;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ShortcutKey = 'F8';
                ToolTip = 'Scan a gas cylinder to add to the document.';

                trigger OnAction()
                var
                    GasCylinderFillingMgt: Codeunit "Gas Cylinder Filling Mgt. GAS";
                begin
                    GasCylinderFillingMgt.ScanCylinder(Rec);
                    CurrPage.Update(false);
                end;
            }

            action(Release)
            {
                ApplicationArea = All;
                Caption = 'Re&lease';
                Image = ReleaseDoc;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ShortcutKey = 'Ctrl+F9';
                ToolTip = 'Release the document for the next step in processing.';
                Enabled = Rec.Status = Rec.Status::Open;

                trigger OnAction()
                var
                    GasCylinderFillingMgt: Codeunit "Gas Cylinder Filling Mgt. GAS";
                begin
                    GasCylinderFillingMgt.ReleaseDocument(Rec);
                end;
            }

            action(Reopen)
            {
                ApplicationArea = All;
                Caption = 'Re&open';
                Image = ReOpen;
                Promoted = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                ToolTip = 'Reopen the document for editing.';
                Enabled = Rec.Status = Rec.Status::Released;

                trigger OnAction()
                var
                    GasCylinderFillingMgt: Codeunit "Gas Cylinder Filling Mgt. GAS";
                begin
                    GasCylinderFillingMgt.ReopenDocument(Rec);
                end;
            }

            action(Post)
            {
                ApplicationArea = All;
                Caption = 'P&ost';
                Image = PostDocument;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ShortcutKey = 'F9';
                ToolTip = 'Post the document to update cylinder gas item associations.';
                Enabled = Rec.Status = Rec.Status::Released;

                trigger OnAction()
                var
                    GasCylinderFillingMgt: Codeunit "Gas Cylinder Filling Mgt. GAS";
                begin
                    GasCylinderFillingMgt.PostDocument(Rec);
                end;
            }
        }
    }

    var
        StatusStyleTxt: Text;
        CylinderInfoActionEnabled: Boolean;
        DocumentEditable: Boolean;

    trigger OnAfterGetRecord()
    begin
        StyleStatusText();
        SetControlsVisibility();
    end;

    trigger OnAfterGetCurrRecord()
    begin
        SetControlsVisibility();
    end;

    local procedure StyleStatusText()
    begin
        StatusStyleTxt := '';

        case Rec.Status of
            Rec.Status::Open:
                StatusStyleTxt := Format(PageStyle::Standard);
            Rec.Status::Released:
                StatusStyleTxt := Format(PageStyle::Favorable);
            Rec.Status::Posted:
                StatusStyleTxt := Format(PageStyle::Subordinate);
        end;
    end;

    local procedure SetControlsVisibility()
    var
        GasCylinderFillingLine: Record "Gas Cylinder Filling Line GAS";
    begin
        CylinderInfoActionEnabled := false;

        // Only enable cylinder info action if there's at least one line
        GasCylinderFillingLine.SetRange("Document No.", Rec."No.");
        CylinderInfoActionEnabled := not GasCylinderFillingLine.IsEmpty();

        // Make all fields uneditable when document is Posted
        DocumentEditable := Rec.Status <> Rec.Status::Posted;
    end;
}