permissionset 60000 "GasManagement GAS"
{
    Caption = 'Gas Management', Locked = true;
    Assignable = true;
    Permissions = codeunit "Gas Cylinder Transfer Mgt. GAS" = X,
        codeunit "Gas Cylinder QC Helper GAS" = X,
        codeunit "Gas Cylinder Filling Mgt. GAS" = X,
        page "Gas Cylinder Barcode GAS" = X,
        page "Transfer Header GAS" = X,
        page "Transfer Line GAS" = X,
        page "Gas Cylinders at Customer GAS" = X,
        page "Gas Cylinder Filling Card GAS" = X,
        page "Gas Cylinder Filling List GAS" = X,
        page "Gas Cyld. Filling Subform GAS" = X,
        table "Cylinder Type GAS" = X,
        tabledata "Cylinder Type GAS" = RIMD,
        table "Gas Cylinder Filling Line GAS" = X,
        tabledata "Gas Cylinder Filling Line GAS" = RIMD,
        page "Cylinder Type List GAS" = X,
        page "Cylinder Type Card GAS" = X,
        page "Gas Cylinder Management RC GAS" = X,
        page "Gas Cylinder Activities GAS" = X,
        table "Gas Cylinder Cue GAS" = X,
        tabledata "Gas Cylinder Cue GAS" = RIMD,
        tabledata "Gas Cyld. Filling Header GAS" = RIMD,
        table "Gas Cyld. Filling Header GAS" = X,
        tabledata "Gas Cyl. Mgt. Setup GAS" = RIMD,
        table "Gas Cyl. Mgt. Setup GAS" = X,
        page "Gas Cyl. Mgt. Setup GAS" = X,
        page "Gas Cylinder QC Scanner GAS" = X,
        page "Reservation Entries GAS" = X,
        codeunit "Gas Cylinder Sales Mngt. GAS" = X,
        table "GasDefTankPerLoc GAS" = X,
        tabledata "GasDefTankPerLoc GAS" = RIMD,
        page "GasDefTankPerLocList GAS" = X,
        codeunit "Mixed Gas Management GAS" = X,
        tabledata "Cylinder Entry/Exit Header GAS" = RIMD,
        tabledata "Cylinder Entry/Exit Line GAS" = RIMD,
        table "Cylinder Entry/Exit Header GAS" = X,
        table "Cylinder Entry/Exit Line GAS" = X,
        page "Cylinder Entry/Exit Header GAS" = X,
        page "Cylinder Entry/Exit List GAS" = X,
        page CylinderEntryExitLineSubform = X,
        codeunit GasCylinderBarcodeHandlerGAS = X,
        codeunit "Company Management GAS" = X,
        page "Company Management List GAS" = X,
        tabledata "Company Information" = RIMD,
        tabledata Company = RD;
}