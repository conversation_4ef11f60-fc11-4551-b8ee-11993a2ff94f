table 60000 "Gas Cyld. Filling Header GAS"
{
    Caption = 'Gas Cylinder Filling Header';
    DataClassification = CustomerContent;
    LookupPageId = "Gas Cylinder Filling Card GAS";
    DrillDownPageId = "Gas Cylinder Filling List GAS";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            ToolTip = 'Specifies the number of the gas cylinder filling document.';
            trigger OnValidate()
            begin
                if "No." <> xRec."No." then begin
                    GetGasCylinderMgtSetup();
                    NoSeries.TestManual(GasCylinderMgtSetup."Gas Cylinder Filling Nos. GAS");
                    "No. Series" := '';
                end;
            end;
        }
        field(2; "Document Date"; Date)
        {
            Caption = 'Document Date';
            ToolTip = 'Specifies the date when the document was created.';
            trigger OnValidate()
            begin
                if "Document Date" <> 0D then
                    Validate("Posting Date", "Document Date");
            end;
        }
        field(3; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the date when the document will be posted.';
            trigger OnValidate()
            begin
                if "Posting Date" = 0D then
                    "Posting Date" := WorkDate();
            end;
        }
        field(8; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            Editable = false;
            TableRelation = "No. Series";
            AllowInCustomizations = Always;
        }
        field(9; Status; Enum "Gas Cyl. Filling Status GAS")
        {
            Caption = 'Status';
            Editable = false;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the status of the document.';
        }
        field(10; "Total Quantity"; Decimal)
        {
            Caption = 'Total Quantity';
            FieldClass = FlowField;
            CalcFormula = sum("Gas Cylinder Filling Line GAS".Quantity where("Document No." = field("No.")));
            Editable = false;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the total quantity of gas being filled.';
        }
        field(11; "No. of Cylinders"; Integer)
        {
            Caption = 'No. of Cylinders';
            Editable = false;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the number of unique cylinders being filled.';
        }
        field(12; "User ID"; Code[50])
        {
            Caption = 'User ID';
            Editable = false;
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the user who created the document.';
        }

    }
    keys
    {
        key(Key1; "No.")
        {
            Clustered = true;
        }
        key(Key2; "Posting Date") { }
    }
    trigger OnInsert()
    var
    // DefaultLocation: Record Location;
    begin
        if "No." = '' then begin
            GetGasCylinderMgtSetup();
            GasCylinderMgtSetup.TestField("Gas Cylinder Filling Nos. GAS");
            "No. Series" := GasCylinderMgtSetup."Gas Cylinder Filling Nos. GAS";
            if NoSeries.AreRelated(GasCylinderMgtSetup."Gas Cylinder Filling Nos. GAS", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;

        Status := Status::Open;
        "Document Date" := WorkDate();
        "Posting Date" := WorkDate();
        "User ID" := CopyStr(UserId(), 1, MaxStrLen("User ID"));

        // DefaultLocation.SetRange("Default Gas Fill Location GAS", true);
        // if DefaultLocation.FindFirst() then
        //     "To Location Code" := DefaultLocation.Code;
    end;

    trigger OnDelete()
    var
        GasCylinderFillingLine: Record "Gas Cylinder Filling Line GAS";
    begin
        TestField(Status, Status::Open);

        GasCylinderFillingLine.SetRange("Document No.", "No.");
        GasCylinderFillingLine.DeleteAll(true);
    end;

    local procedure GetGasCylinderMgtSetup()
    begin
        if not GasCylinderMgtSetupRead then begin
            GasCylinderMgtSetup.GetSetup();
            GasCylinderMgtSetupRead := true;
        end;
    end;

    procedure UpdateNoOfCylinders()
    var
        GasCylinderFillingLine: Record "Gas Cylinder Filling Line GAS";
        xGasCylinderFillingLine: Record "Gas Cylinder Filling Line GAS";
        CylinderCount: Integer;
    begin
        GasCylinderFillingLine.SetRange("Document No.", Rec."No.");
        if GasCylinderFillingLine.FindSet() then begin
            CylinderCount := 1;
            xGasCylinderFillingLine := GasCylinderFillingLine;
            repeat
                if xGasCylinderFillingLine."Cylinder Serial No." <> GasCylinderFillingLine."Cylinder Serial No." then begin
                    CylinderCount += 1;
                    xGasCylinderFillingLine := GasCylinderFillingLine;
                end;
            until GasCylinderFillingLine.Next() = 0;
        end;

        Rec.Validate("No. of Cylinders", CylinderCount);
        Rec.Modify(true);

        Message('Number of unique cylinders counted: %1', CylinderCount);
    end;




    var
        GasCylinderMgtSetup: Record "Gas Cyl. Mgt. Setup GAS";
        NoSeries: Codeunit "No. Series";
        GasCylinderMgtSetupRead: Boolean;
}
