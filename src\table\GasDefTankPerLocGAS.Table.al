table 60003 "GasDefTankPerLoc GAS"
{
    Caption = 'Gas Default Tank Per Location';
    DataClassification = CustomerContent;
    LookupPageId = "GasDefTankPerLocList GAS";
    DrillDownPageId = "GasDefTankPerLocList GAS";

    fields
    {
        field(1; "Location Code"; Code[10])
        {
            Caption = 'Location Code';
            TableRelation = Location;
            NotBlank = true;
            ToolTip = 'Specifies the location code where the default gas tank is defined.';

            trigger OnValidate()
            var
                Location: Record Location;
            begin
                if "Location Code" <> '' then begin
                    Location.Get("Location Code");
                    "Location Name" := Location.Name;
                end else
                    "Location Name" := '';
            end;
        }
        field(2; "Gas Item No."; Code[20])
        {
            Caption = 'Gas Item No.';
            TableRelation = Item;
            NotBlank = true;
            ToolTip = 'Specifies the default gas item number for this location.';

            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if "Gas Item No." <> '' then begin
                    Item.Get("Gas Item No.");
                    "Gas Item Description" := Item.Description;
                end else
                    "Gas Item Description" := '';
            end;
        }
        field(3; "Gas Item Description"; Text[100])
        {
            Caption = 'Gas Item Description';
            Editable = false;
            ToolTip = 'Specifies the description of the default gas item.';
        }
        field(4; "Default Tank Serial No."; Code[50])
        {
            Caption = 'Default Tank Serial No.';
            TableRelation = "Serial No. Information"."Serial No." where("Gas Item No. GAS" = field("Gas Item No."));
            ToolTip = 'Specifies the default tank serial number for this location and gas item.';

            trigger OnValidate()
            var
                SerialNoInfo: Record "Serial No. Information";
            begin
                if "Default Tank Serial No." <> '' then begin
                    SerialNoInfo.Reset();
                    SerialNoInfo.SetRange("Serial No.", "Default Tank Serial No.");
                    if SerialNoInfo.FindFirst() then begin
                        if SerialNoInfo."Gas Item No. GAS" <> "Gas Item No." then
                            Error(TankSerialNoMismatchErr);
                    end else
                        Error(TankSerialNoNotFoundErr);
                end;
            end;
        }
        field(5; "Location Name"; Text[100])
        {
            Caption = 'Location Name';
            Editable = false;
            ToolTip = 'Specifies the name of the location.';
        }
    }

    keys
    {
        key(PK; "Location Code", "Gas Item No.")
        {
            Clustered = true;
        }
    }

    var
        TankSerialNoNotFoundErr: Label 'The specified tank serial number does not exist.';
        TankSerialNoMismatchErr: Label 'The specified tank serial number is not associated with the selected gas item.';
}
