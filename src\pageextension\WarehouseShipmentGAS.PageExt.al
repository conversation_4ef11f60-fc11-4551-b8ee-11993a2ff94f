pageextension 60007 "Warehouse Shipment GAS" extends "Warehouse Shipment" // Page 7322
{
    actions
    {
        addafter("&Shipment") // Or choose another appropriate group like "F&unctions" or a new group
        {
            action("Read Gas Cylinder Barcode GAS")
            {
                ApplicationArea = All;
                Caption = 'Read Gas Cylinder Barcode';
                Image = BarCode;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Scan a gas cylinder barcode to assign item tracking to the warehouse shipment line.';

                trigger OnAction()
                var
                    GasCylinderSalesMgt: Codeunit "Gas Cylinder Sales Mngt. GAS";
                begin
                    GasCylinderSalesMgt.ReadGasCylinderBarcodeForWhseShipment(Rec);
                end;
            }
        }
    }
}
