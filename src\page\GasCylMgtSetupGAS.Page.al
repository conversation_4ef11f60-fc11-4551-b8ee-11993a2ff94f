page 60009 "Gas Cyl. Mgt. Setup GAS" // Changed object name
{
    PageType = Card;
    ApplicationArea = All;
    UsageCategory = Administration;
    SourceTable = "Gas Cyl. Mgt. Setup GAS";
    Caption = 'Gas Cylinder Management Setup';

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                field("Gas Cylinder Filling Nos. GAS"; Rec."Gas Cylinder Filling Nos. GAS")
                {
                }
                field("Cylinder Filling Template Name"; Rec."Cylinder Filling Template Name")
                {
                }
                field("Cylinder Filling Batch Name"; Rec."Cylinder Filling Batch Name")
                {
                }
                field("Cylinder Entry/Exit Nos. GAS"; Rec."Cylinder Entry/Exit Nos. GAS")
                {
                }
            }
        }
    }

    trigger OnOpenPage()
    begin
        Rec.GetSetup();
    end;
}