tableextension 60000 "Serial No. Information GAS" extends "Serial No. Information"
{
    fields
    {
        field(60000; "Gas Item No. GAS"; Code[20])
        {
            Caption = 'Gas Item No.';
            TableRelation = Item;
            ToolTip = 'Specifies the gas item number associated with this serial number.';
            trigger OnValidate()
            var
                Item: Record Item;
            begin
                if "Gas Item No. GAS" <> '' then begin
                    Item.Get("Gas Item No. GAS");
                    "Gas Item Description GAS" := Item.Description;
                end else
                    "Gas Item Description GAS" := '';
            end;
        }
        field(60001; "Gas Item Description GAS"; Text[100])
        {
            Caption = 'Gas Item Description';
            Editable = false;
            ToolTip = 'Shows the description of the gas item.';
        }
        field(60002; "Gas Quantity GAS"; Decimal)
        {
            Caption = 'Gas Quantity';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry".Quantity where("Item No." = field("Gas Item No. GAS"), "Package No." = field("Serial No."), "Posting Date" = field("Date Filter")));
            ToolTip = 'Shows the quantity of gas associated with this serial number.';
        }
        field(60003; "Location Code GAS"; Code[10])
        {
            Caption = 'Location Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Item Ledger Entry"."Location Code" where("Item No." = field("Item No."),
                                                                            "Variant Code" = field("Variant Code"),
                                                                            "Serial No." = field("Serial No."),
                                                                            Open = const(true)));
            ToolTip = 'Shows the location code where this serial number is currently stored.';
        }
        field(60004; "Cylinder Type GAS"; Code[20])
        {
            Caption = 'Cylinder Type';
            TableRelation = "Cylinder Type GAS".Code;
            ToolTip = 'Specifies the type of cylinder associated with this serial number.';

            trigger OnValidate()
            var
                CylinderType: Record "Cylinder Type GAS";
            begin
                if "Cylinder Type GAS" <> '' then begin
                    CylinderType.Get("Cylinder Type GAS");
                    "Cylinder Type Description GAS" := CylinderType.Description;
                end else
                    "Cylinder Type Description GAS" := '';
            end;
        }
        field(60005; "Cylinder Type Description GAS"; Text[100])
        {
            Caption = 'Cylinder Type Description';
            Editable = false;
            ToolTip = 'Shows the description of the cylinder type.';
        }
        field(60006; "Barcode GAS"; Text[250])
        {
            Caption = 'Barcode';
            ToolTip = 'Specifies the barcode associated with this serial number.';
        }
        field(60007; "Production Date GAS"; Date)
        {
            Caption = 'Production Date';
            ToolTip = 'Specifies the production date of the cylinder.';
        }
        field(60008; "Ministry ID No. GAS"; Text[100])
        {
            Caption = 'Ministry ID No.';
            ToolTip = 'Specifies the Ministry ID Number associated with this cylinder.';
        }
        field(60009; "Ministry Notification Type GAS"; Text[100])
        {
            Caption = 'Ministry Notification Type';
            ToolTip = 'Specifies the Ministry Notification Type for this cylinder.';
        }
        field(60010; "Ministry Manufacturer Name GAS"; Text[100])
        {
            Caption = 'Ministry Manufacturer Name';
            ToolTip = 'Specifies the name of the manufacturer registered with the ministry.';
        }
        field(60011; "Ministry Exporter Name GAS"; Text[100])
        {
            Caption = 'Ministry Exporter Name';
            ToolTip = 'Specifies the name of the exporter registered with the ministry.';
        }
        field(60012; "Office Name GAS"; Text[100])
        {
            Caption = 'Office Name';
            ToolTip = 'Specifies the name of the office responsible for this cylinder.';
        }
        field(60013; "Cylinder Owner Code GAS"; Text[50])
        {
            Caption = 'Cylinder Owner Code';
            ToolTip = 'Specifies the code of the cylinder owner.';
        }
        field(60014; "Cylinder Owner Name GAS"; Text[100])
        {
            Caption = 'Cylinder Owner Name';
            ToolTip = 'Specifies the name of the cylinder owner.';
        }
        field(60015; "Product Group GAS"; Text[50])
        {
            Caption = 'Product Group';
            ToolTip = 'Specifies the product group of the cylinder.';
        }
        field(60016; "Color GAS"; Text[50])
        {
            Caption = 'Color';
            ToolTip = 'Specifies the color of the cylinder.';
        }
        field(60017; "Volume GAS"; Text[50])
        {
            Caption = 'Volume';
            ToolTip = 'Specifies the volume of the cylinder.';
        }
        field(60018; "Full Weight GAS"; Decimal)
        {
            Caption = 'Full Weight (Kg)';
            ToolTip = 'Specifies the full weight of the cylinder in kilograms.';
        }
        field(60019; "Empty Weight GAS"; Decimal)
        {
            Caption = 'Empty Weight (Kg)';
            ToolTip = 'Specifies the empty weight of the cylinder in kilograms.';
        }
        field(60020; "Independent Serial No. GAS"; Text[50])
        {
            Caption = 'Independent Serial No.';
            ToolTip = 'Specifies the independent serial number of the cylinder.';
        }
        field(60021; "Manufacturer GAS"; Text[50])
        {
            Caption = 'Manufacturer';
            ToolTip = 'Specifies the manufacturer of the cylinder.';
        }
        field(60022; "Production Number GAS"; Text[50])
        {
            Caption = 'Production Number';
            ToolTip = 'Specifies the production number of the cylinder.';
        }
        field(60023; "Test Pressure GAS"; Decimal)
        {
            Caption = 'Test Pressure';
            ToolTip = 'Specifies the test pressure of the cylinder.';
        }
        field(60024; "Operating Pressure GAS"; Decimal)
        {
            Caption = 'Operating Pressure';
            ToolTip = 'Specifies the operating pressure of the cylinder.';
        }
        field(60025; "Working Pressure GAS"; Decimal)
        {
            Caption = 'Working Pressure';
            ToolTip = 'Specifies the working pressure of the cylinder.';
        }
        field(60026; "Hand Strength GAS"; Integer)
        {
            Caption = 'Hand Strength';
            ToolTip = 'Specifies the hand strength of the cylinder.';
        }
    }
}