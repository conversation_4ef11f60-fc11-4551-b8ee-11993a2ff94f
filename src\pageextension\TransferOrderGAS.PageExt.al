pageextension 60006 "Transfer Order GAS" extends "Transfer Order"
{
    actions
    {
        addafter("F&unctions")
        {
            action("Read Gas Cylinder Barcode GAS")
            {
                ApplicationArea = All;
                Caption = 'Read Gas Cylinder Barcode';
                Image = BarCode;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                ToolTip = 'Scan a gas cylinder barcode to add it to the transfer order.';

                trigger OnAction()
                var
                    GasCylinderTransferMgt: Codeunit "Gas Cylinder Transfer Mgt. GAS";
                begin
                    GasCylinderTransferMgt.ReadGasCylinderBarcodeForTransfer(Rec);
                end;
            }
        }
    }
}