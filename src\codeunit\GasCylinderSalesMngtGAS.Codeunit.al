codeunit 60000 "Gas Cylinder Sales Mngt. GAS"
{
    procedure ReadGasCylinderBarcodeForWhseShipment(WhseShipmentHeader: Record "Warehouse Shipment Header")
    var
        BarcodePage: Page "Gas Cylinder Barcode GAS";
        SerialNo: Code[50];
    begin
        BarcodePage.LookupMode(true);
        if BarcodePage.RunModal() = Action::LookupOK then begin
            SerialNo := BarcodePage.GetBarcodeValue();
            if SerialNo <> '' then
                ProcessScannedCylinderForWhseShipment(SerialNo, WhseShipmentHeader);
        end;
    end;

    local procedure ProcessScannedCylinderForWhseShipment(SerialNo: Code[50]; WhseShipmentHeader: Record "Warehouse Shipment Header")
    var
        SerialNoInfo: Record "Serial No. Information";
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
        GasItemNo: Code[20];
        GasQuantity: Decimal;
    begin
        ValidateGasCylinderInfo(SerialNo, SerialNoInfo, GasItemNo, GasQuantity);
        FindAndMarkEligibleWhseShipmentLines(WhseShipmentHeader, GasItemNo, GasQuantity, WarehouseShipmentLine);
        AssignGasCylinderToWhseShipmentLine(WarehouseShipmentLine, SerialNoInfo, SerialNo, GasItemNo, GasQuantity);
    end;

    local procedure ValidateGasCylinderInfo(SerialNo: Code[50]; var SerialNoInfo: Record "Serial No. Information"; var GasItemNo: Code[20]; var GasQuantity: Decimal)
    begin
        SerialNoInfo.SetLoadFields("Serial No.", "Gas Item No. GAS");
        SerialNoInfo.SetRange("Serial No.", SerialNo);
        if not SerialNoInfo.FindFirst() then
            Error(GasCylinderNotFoundLbl, SerialNo);

        SerialNoInfo.CalcFields("Gas Quantity GAS");
        GasItemNo := SerialNoInfo."Gas Item No. GAS";
        GasQuantity := SerialNoInfo."Gas Quantity GAS";

        if GasItemNo = '' then
            Error(NoGasItemAssociatedLbl, SerialNo);

        if GasQuantity <= 0 then
            Error(NoGasQuantityInCylinderLbl, SerialNo);
    end;

    local procedure FindAndMarkEligibleWhseShipmentLines(WhseShipmentHeader: Record "Warehouse Shipment Header"; GasItemNo: Code[20]; GasQuantity: Decimal; var WarehouseShipmentLine: Record "Warehouse Shipment Line")
    begin
        WarehouseShipmentLine.SetLoadFields("No.", "Item No.", "Qty. Outstanding");
        WarehouseShipmentLine.SetRange("No.", WhseShipmentHeader."No.");
        WarehouseShipmentLine.SetRange("Item No.", GasItemNo);

        if not WarehouseShipmentLine.FindSet() then
            Error(NoMatchingWhseLineLbl, GasItemNo, WhseShipmentHeader."No.");

        repeat
            WarehouseShipmentLine.CalcFields("Total Tracked Qty. GAS");
            if (WarehouseShipmentLine."Qty. Outstanding" + WarehouseShipmentLine."Total Tracked Qty. GAS") >= GasQuantity then
                WarehouseShipmentLine.Mark(true);
        until WarehouseShipmentLine.Next() = 0;

        WarehouseShipmentLine.MarkedOnly(true);
        if not WarehouseShipmentLine.FindFirst() then
            Error(NoMatchingWhseLineLbl, GasItemNo, WhseShipmentHeader."No.");
    end;

    local procedure AssignGasCylinderToWhseShipmentLine(var WarehouseShipmentLine: Record "Warehouse Shipment Line"; SerialNoInfo: Record "Serial No. Information"; SerialNo: Code[50]; GasItemNo: Code[20]; GasQuantity: Decimal)
    begin
        AssignItemTrackingToWhseShipmentLine(WarehouseShipmentLine, SerialNoInfo, GasQuantity);
        Message(GasCylinderAssignedToShipmentLbl, SerialNo, GasItemNo, WarehouseShipmentLine."Line No.");
    end;

    local procedure AssignItemTrackingToWhseShipmentLine(var WarehouseShipmentLine: Record "Warehouse Shipment Line"; SerialNoInfo: Record "Serial No. Information"; QtyToAssign: Decimal)
    var
        Item: Record Item;
        ItemTrackingCode: Record "Item Tracking Code";
        SalesLine: Record "Sales Line";
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        WarehouseShipmentHeader: Record "Warehouse Shipment Header";
        SalesLineReserve: Codeunit "Sales Line-Reserve";
        ItemTrackingLines: Page "Item Tracking Lines";
        BlankItemTrackingCodeErr: Label 'Item %1 does not have item tracking set up.', Comment = '%1=Item."No."';
    begin
        WarehouseShipmentHeader.Get(WarehouseShipmentLine."No.");
        Item.Get(WarehouseShipmentLine."Item No.");

        if Item."Item Tracking Code" = '' then
            Error(BlankItemTrackingCodeErr, Item."No.");

        ItemTrackingCode.Get(Item."Item Tracking Code");

        // Get the source document line
        SalesLine.Get(WarehouseShipmentLine."Source Subtype", WarehouseShipmentLine."Source No.", WarehouseShipmentLine."Source Line No.");
        SalesLineReserve.InitFromSalesLine(TempSourceTrackingSpecification, SalesLine);

        // Initialize tracking specification
        TempTrackingSpecification.Init();
        TempTrackingSpecification.SetItemData(
            WarehouseShipmentLine."Item No.",
            WarehouseShipmentLine.Description,
            WarehouseShipmentLine."Location Code",
            WarehouseShipmentLine."Variant Code",
            '', // Bin Code - may need to be adapted based on your requirements
            WarehouseShipmentLine."Qty. per Unit of Measure");

        // Use Serial No. as Package No. for gas cylinders
        TempTrackingSpecification."Package No." := SerialNoInfo."Serial No.";

        // Set up quantities
        TempTrackingSpecification.SetQuantities(QtyToAssign,
                                                QtyToAssign,
                                                QtyToAssign * WarehouseShipmentLine."Qty. per Unit of Measure",
                                                0,
                                                0,
                                                WarehouseShipmentLine."Qty. Shipped (Base)",
                                                0);

        TempTrackingSpecification.Insert(false);

        // Register item tracking
        //ItemTrackingLines.SetBlockCommit(true);
        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, WorkDate(), TempTrackingSpecification);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"Create E-Shipment NAV Doc. INF", OnAfterCreateEShipmentLinesFromWhShipment, '', false, false)]
    local procedure "Create E-Shipment NAV Doc. INF_OnAfterCreateEShipmentLinesFromWhShipment"(PostedWhseShipmentHeader: Record "Posted Whse. Shipment Header"; var EShipmentHeader: Record "E-Shipment Header INF")
    var
        PostedWhseShipmentLine: Record "Posted Whse. Shipment Line";
        SalesShipmentLine: Record "Sales Shipment Line";
        TempItemLedgerEntry: Record "Item Ledger Entry" temporary;
        TransferHeader: Record "Transfer Header";
        Location: Record Location;
        WarehouseShipmentHeader: Record "Warehouse Shipment Header";
        WarehouseShipmentLine: Record "Warehouse Shipment Line";
        ItemTrackingDocManagement: Codeunit "Item Tracking Doc. Management";
        GetSourceDocOutbound: Codeunit "Get Source Doc. Outbound";
        WhsePostShipment: Codeunit "Whse.-Post Shipment";
    begin
        PostedWhseShipmentLine.SetRange("No.", PostedWhseShipmentHeader."No.");
        if PostedWhseShipmentLine.FindSet() then begin
            // Find the consignment location for the customer
            Location.SetRange("Consignment Customer No. INF", PostedWhseShipmentHeader."Sell-to Source No. INF");
            Location.SetRange("Consignment Ship-to Code INF", PostedWhseShipmentHeader."Ship-to Code INF");
            if not Location.FindFirst() then
                exit;

            // Create a single transfer order
            TransferHeader.Init();
            TransferHeader.Insert(true);
            TransferHeader.Validate("Transfer-from Code", PostedWhseShipmentHeader."Location Code");
            TransferHeader.Validate("Transfer-to Code", Location."Code");
            TransferHeader.Validate("In-Transit Code", 'NAKLIYE');
            TransferHeader.Modify(true);

            // Process each shipment line and its item ledger entries
            repeat
                SalesShipmentLine.Get(PostedWhseShipmentLine."Posted Source No.", PostedWhseShipmentLine."Source Line No.");
                ItemTrackingDocManagement.RetrieveEntriesFromShptRcpt(TempItemLedgerEntry, Database::"Sales Shipment Line", 0,
                    SalesShipmentLine."Document No.", '', 0, SalesShipmentLine."Line No.");

                // Process each package number from item ledger entries
                if TempItemLedgerEntry.FindSet() then
                    repeat
                        if TempItemLedgerEntry."Package No." <> '' then
                            ProcessPackageNoToTransfer(TempItemLedgerEntry, TransferHeader);
                    until TempItemLedgerEntry.Next() = 0;
            until PostedWhseShipmentLine.Next() = 0;
            if TransferHeader.Status <> TransferHeader.Status::Released then begin
                Codeunit.Run(Codeunit::"Release Transfer Document", TransferHeader);
                Commit();//

            end;
            GetSourceDocOutbound.CreateFromOutbndTransferOrder(TransferHeader);
            //Post Warehouse Shipment that created from "GetSourceDocOutbound.CreateFromOutbndTransferOrder(TransferHeader);" function <Placeholder>
            WarehouseShipmentHeader.SetRange("Source No. INF", TransferHeader."No.");
            if not WarehouseShipmentHeader.FindFirst() then
                exit;

            WarehouseShipmentLine.SetRange("No.", WarehouseShipmentHeader."No.");
            if not WarehouseShipmentLine.FindFirst() then
                exit;

            WhsePostShipment.Run(WarehouseShipmentLine);

            //Receive the transfer order
            if TransferHeader.Status = TransferHeader.Status::Released then begin
                IsAutoReceivingTransferGlobalFlag := true;
                Codeunit.Run(Codeunit::"TransferOrder-Post (Yes/No)", TransferHeader);
                IsAutoReceivingTransferGlobalFlag := false;
                //Commit(); // Commit after posting receipt
            end;

        end;
        Message('Transfer Order %1 created for gas cylinders.', TransferHeader."No.");
    end;

    local procedure ProcessPackageNoToTransfer(ItemLedgerEntry: Record "Item Ledger Entry"; TransferHeader: Record "Transfer Header")
    var
        SerialNoInfo: Record "Serial No. Information";
        GasCylinderTransferMgt: Codeunit "Gas Cylinder Transfer Mgt. GAS";
        SerialNo: Code[50];
    begin
        SerialNo := ItemLedgerEntry."Package No.";
        if SerialNo = '' then
            exit;

        // Check if this package no. exists as a serial number
        SerialNoInfo.SetRange("Serial No.", SerialNo);
        if SerialNoInfo.IsEmpty() then
            exit;

        // Use the existing transfer management function to process the cylinder
        GasCylinderTransferMgt.ProcessScannedCylinderForTransfer(SerialNo, TransferHeader);
    end;

    [EventSubscriber(ObjectType::Codeunit, Codeunit::"TransferOrder-Post (Yes/No)", OnCodeOnBeforePostTransferOrder, '', false, false)]
    local procedure AutoReceiveTransferOrderOnPost(
        var TransHeader: Record "Transfer Header";
        var DefaultNumber: Integer;
        var Selection: Option; // Corresponds to Option " ",Shipment,Receipt in the publisher
        var IsHandled: Boolean;
        var PostBatch: Boolean; // Corrected parameter name to match publisher
        var TransferOrderPost: Enum "Transfer Order Post"; // Corrected parameter name to match publisher
        PreviewMode: Boolean // Corrected: PreviewMode is not a VAR parameter in the publisher
    )
    begin
        if not IsAutoReceivingTransferGlobalFlag then
            exit;

        PostBatch := true; // Modify the publisher's variable
        TransferOrderPost := TransferOrderPost::Receive; // Modify the publisher's variable
        IsHandled := true; // Indicate that we've handled the logic, so standard dialog is skipped.
    end;

    var
        GasCylinderNotFoundLbl: Label 'Gas cylinder with serial number %1 not found.', Comment = '%1 = Serial Number';
        NoGasItemAssociatedLbl: Label 'No gas item associated with cylinder serial number %1.', Comment = '%1 = Serial Number';
        NoGasQuantityInCylinderLbl: Label 'Gas cylinder %1 has no gas quantity.', Comment = '%1 = Serial Number';
        NoMatchingWhseLineLbl: Label 'No matching warehouse shipment line found for item %1 in document %2 with sufficient assignable quantity.', Comment = '%1 = Item No., %2 = Document No.';
        GasCylinderAssignedToShipmentLbl: Label 'Gas cylinder %1 (Gas Item %2) assigned to warehouse shipment line %3.', Comment = '%1 = Serial No., %2 = Gas Item No., %3 = Line No.';
        IsAutoReceivingTransferGlobalFlag: Boolean; // Added for auto-receive logic

}