page 60003 "Gas Cylinders at Customer GAS"
{
    ApplicationArea = All;
    Caption = 'Gas Cylinders at Customer Locations';
    PageType = List;
    SourceTable = "Item Ledger Entry";
    UsageCategory = Lists;
    Editable = false;
    SourceTableView = where(Open = const(true), "Serial No." = filter(<> ''), "Consignment Customer No. GAS" = filter(<> ''));

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Serial No."; Rec."Serial No.")
                {
                    ToolTip = 'Specifies the serial number of the gas cylinder.';
                    StyleExpr = StyleTxt;
                }
                field("Item No."; Rec."Item No.")
                {
                    ToolTip = 'Specifies the item number of the gas cylinder.';
                    StyleExpr = StyleTxt;
                }
                field("Item Description"; Rec.Description)
                {
                    Caption = 'Description';
                    ToolTip = 'Specifies the description of the gas cylinder.';
                    StyleExpr = StyleTxt;
                }
                field("Location Code"; Rec."Location Code")
                {
                    ToolTip = 'Specifies the location where the gas cylinder is currently kept.';
                    StyleExpr = StyleTxt;
                }
                field("Consignment Customer No."; Rec."Consignment Customer No. GAS")
                {
                    ToolTip = 'Specifies the customer number for consignment inventory.';
                    StyleExpr = StyleTxt;
                }

                field("Consignment Ship-to Code"; Rec."Consignment Ship-to Code GAS")
                {
                    ToolTip = 'Specifies the ship-to code for the consignment location.';
                    StyleExpr = StyleTxt;
                }
                field(CustomerName; CustomerName)
                {
                    Caption = 'Customer Name';
                    ToolTip = 'Specifies the name of the customer who has the gas cylinder.';
                    StyleExpr = StyleTxt;
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    Caption = 'Transfer Date';
                    ToolTip = 'Specifies the date when the gas cylinder was transferred to the customer location.';
                    StyleExpr = StyleTxt;
                }

                field(MaxCylinderReturnTime; MaxCylinderReturnTime)
                {
                    Caption = 'Max Return Time';
                    ToolTip = 'Specifies the maximum time period the customer can keep gas cylinders before returning them.';
                    StyleExpr = StyleTxt;
                }
                field(DaysAtLocation; DaysAtLocation)
                {
                    Caption = 'Days at Location';
                    ToolTip = 'Specifies for how many days the gas cylinder has been at the customer location.';
                    StyleExpr = StyleTxt;
                }
            }
        }
    }
    var
        CustomerName: Text[250];
        MaxCylinderReturnTime: Text[50];
        DaysAtLocation: Integer;
        StyleTxt: Text;

    trigger OnAfterGetRecord()
    var
        Cust: Record Customer;
        AllowedReturnDateFormula: DateFormula;
        ReturnByDate: Date;
    begin
        // Initialize variables for each record
        DaysAtLocation := 0;
        CustomerName := '';
        MaxCylinderReturnTime := '';
        StyleTxt := ''; // Default style

        Rec.CalcFields("Consignment Customer No. GAS");

        // Check if it's a consignment location by checking the Consignment Customer No.
        if Rec."Consignment Customer No. GAS" <> '' then begin
            // Calculate days at location
            if Rec."Posting Date" <> 0D then
                DaysAtLocation := Today() - Rec."Posting Date";

            // Get customer details to fetch name and max return time
            if Cust.Get(Rec."Consignment Customer No. GAS") then begin
                CustomerName := Cust.Name;
                AllowedReturnDateFormula := Cust."Max Cylinder Return Time GAS";

                if Format(AllowedReturnDateFormula) <> '' then begin // Check if a formula is set
                    MaxCylinderReturnTime := Format(AllowedReturnDateFormula); // Display the formula as text

                    if Rec."Posting Date" <> 0D then begin
                        ReturnByDate := CalcDate(AllowedReturnDateFormula, Rec."Posting Date");
                        // Check if the cylinder is overdue
                        if (ReturnByDate <> 0D) and (Today() > ReturnByDate) then
                            StyleTxt := Format(PageStyle::Unfavorable) // Standard BC style for overdue/warning
                        else
                            StyleTxt := Format(PageStyle::Favorable); // Standard BC style for on-time/good
                    end else
                        StyleTxt := ''; // Posting date is not set, cannot determine overdue status
                end else begin
                    MaxCylinderReturnTime := 'Not Set'; // Max return time not defined for this customer
                    StyleTxt := ''; // No specific style if return time is not set
                end;
            end else
                CustomerName := 'Customer Not Found'; // Consignment customer number does not exist in Customer table
        end;
        // If not a consignment location, variables remain at their initialized default/empty values.
    end;
}