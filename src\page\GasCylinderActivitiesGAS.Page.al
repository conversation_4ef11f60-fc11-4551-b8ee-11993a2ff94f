page 60007 "Gas Cylinder Activities GAS"
{
    PageType = CardPart;
    ApplicationArea = All;
    Caption = 'Gas Cylinder Activities';
    SourceTable = "Gas Cylinder Cue GAS"; // Added SourceTable

    layout
    {
        area(Content)
        {
            cuegroup(QualityControl)
            {
                Caption = 'Quality Control'; // Example caption
                actions
                {
                    action("New QC Action") // Renamed for clarity if needed
                    {
                        Caption = 'New Quality Control';
                        ApplicationArea = All;
                        //RunObject = codeunit "Gas Cylinder QC Helper GAS";
                        RunObject = page "Gas Cylinder QC Scanner GAS";
                        Image = TileBrickNew;
                        ToolTip = 'Scans a gas cylinder barcode and creates a new production quality control document.';
                        // trigger OnAction()
                        // begin
                        //     Page.Run(Page::"Gas Cylinder QC Scanner GAS");
                        // end;
                    }
                }
            }

            cuegroup(CylinderFilling)
            {
                Caption = 'Cylinder Filling';
                actions
                {
                    action(CreateGasFilling)
                    {
                        Caption = 'New Cylinder Filling';
                        ApplicationArea = All;
                        Image = TileNew;
                        ToolTip = 'Creates a new gas cylinder filling document and opens the barcode scanner.';

                        trigger OnAction()
                        var
                            GasCylFillingHeader: Record "Gas Cyld. Filling Header GAS";
                            GasCylinderFillingMgt: Codeunit "Gas Cylinder Filling Mgt. GAS";
                            FillingCard: Page "Gas Cylinder Filling Card GAS";
                        begin
                            GasCylFillingHeader.Init();
                            GasCylFillingHeader.Insert(true);
                            Commit(); // Commit to ensure the record is saved before using it

                            GasCylFillingHeader.SetRecFilter();
                            FillingCard.SetRecord(GasCylFillingHeader);
                            FillingCard.Run();

                            // Scan cylinder immediately after opening the document
                            GasCylinderFillingMgt.ScanCylinder(GasCylFillingHeader);
                        end;
                    }
                }
            }
        }
    }

    trigger OnOpenPage()
    begin
        Rec.Reset();
        if not Rec.Get() then begin
            Rec.Init();
            Rec.Insert(false); // Use false if you don't want to run OnInsert triggers that might fail if PK is not set
        end;
    end;
}