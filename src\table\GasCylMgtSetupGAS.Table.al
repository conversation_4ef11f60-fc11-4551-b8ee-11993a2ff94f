table 60001 "Gas Cyl. Mgt. Setup GAS"
{
    Caption = 'Gas Cylinder Management Setup';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            DataClassification = SystemMetadata;
            NotBlank = false;
            AllowInCustomizations = Never;
        }
        field(10; "Gas Cylinder Filling Nos. GAS"; Code[20])
        {
            Caption = 'Gas Cylinder Filling Nos.';
            TableRelation = "No. Series";
            ToolTip = 'Specifies the number series code used when assigning numbers to gas cylinder filling documents.';
        }
        field(20; "Cylinder Filling Template Name"; Code[10])
        {
            Caption = 'Cylinder Filling Template Name';
            TableRelation = "Item Journal Template";
            ToolTip = 'Specifies the item journal template to use for cylinder filling posting.';
        }
        field(21; "Cylinder Filling Batch Name"; Code[10])
        {
            Caption = 'Cylinder Filling Batch Name';
            TableRelation = "Item Journal Batch".Name where("Journal Template Name" = field("Cylinder Filling Template Name"));
            ToolTip = 'Specifies the item journal batch to use for cylinder filling posting.';
        }
        field(30; "Cylinder Entry/Exit Nos. GAS"; Code[20])
        {
            Caption = 'Cylinder Entry/Exit Nos.';
            TableRelation = "No. Series";
            ToolTip = 'Specifies the number series code used when assigning numbers to cylinder entry/exit documents.';
        }
    }

    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }

    procedure GetSetup()
    begin
        if not Get('') then begin
            Init();
            Insert(false);
        end;
    end;
}