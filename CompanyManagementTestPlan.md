# Company Management Utility - Test Plan

## Overview
This document outlines the test plan for the Company Management utility functionality that allows users to mark companies for retention and perform bulk deletion of unmarked companies.

## Test Environment Setup
1. Ensure multiple test companies exist in the Business Central environment
2. Assign the "Company Management GAS" permission set to test users
3. Verify that the Gas Management extension is properly installed

## Test Cases

### 1. Company Marking Functionality

#### Test Case 1.1: Mark Company for Keeping
- **Objective**: Verify that companies can be marked for retention
- **Steps**:
  1. Open Company Information page
  2. Navigate to "Company Protection" group
  3. Enable "Keep Company" checkbox
  4. Save the record
- **Expected Result**: Company is marked for protection

#### Test Case 1.2: Company Management List View
- **Objective**: Verify the Company Management List displays all companies with their protection status
- **Steps**:
  1. Open Company Management List page (Page 60015)
  2. Verify all companies are displayed
  3. Check "Keep Company" status for each company
  4. Verify current company is highlighted
- **Expected Result**: All companies displayed with correct status

### 2. Safety Validations

#### Test Case 2.1: Current Company Protection
- **Objective**: Ensure current company cannot be deleted
- **Steps**:
  1. Unmark current company from "Keep Company"
  2. Attempt to run "Delete Unmarked Companies"
- **Expected Result**: Error message preventing deletion of current company

#### Test Case 2.2: At Least One Company Remains
- **Objective**: Ensure at least one company remains after deletion
- **Steps**:
  1. Unmark all companies except one
  2. Run "Delete Unmarked Companies"
- **Expected Result**: Operation should proceed normally

#### Test Case 2.3: Critical Company Protection
- **Objective**: Verify critical companies (CRONUS, EVALUATION, DEMO) are protected
- **Steps**:
  1. Create companies with names containing "CRONUS", "EVALUATION", or "DEMO"
  2. Unmark these companies
  3. Attempt deletion
- **Expected Result**: Error message listing critical companies that cannot be deleted

### 3. Bulk Deletion Operations

#### Test Case 3.1: Successful Bulk Deletion
- **Objective**: Verify unmarked companies are deleted successfully
- **Steps**:
  1. Mark some companies for keeping
  2. Leave others unmarked
  3. Run "Delete Unmarked Companies"
  4. Confirm deletion in dialog
- **Expected Result**: Only unmarked companies are deleted, marked companies remain

#### Test Case 3.2: Deletion Confirmation Dialog
- **Objective**: Verify confirmation dialog shows correct information
- **Steps**:
  1. Prepare companies for deletion
  2. Run "Delete Unmarked Companies"
  3. Check confirmation dialog content
- **Expected Result**: Dialog shows correct count of companies to be deleted

#### Test Case 3.3: Progress Dialog
- **Objective**: Verify progress dialog during deletion
- **Steps**:
  1. Prepare multiple companies for deletion
  2. Run deletion operation
  3. Observe progress dialog
- **Expected Result**: Progress dialog shows current company being processed

### 4. User Interface Tests

#### Test Case 4.1: Company Information Page Extension
- **Objective**: Verify Company Information page shows new field and actions
- **Steps**:
  1. Open Company Information page
  2. Verify "Company Protection" group is visible
  3. Verify "Company Management" actions are available
- **Expected Result**: New UI elements are properly displayed

#### Test Case 4.2: Company Management List Actions
- **Objective**: Verify all actions work correctly in the list page
- **Steps**:
  1. Test "Mark All for Keeping" action
  2. Test "Unmark All for Keeping" action
  3. Test "Refresh" action
  4. Test "Company Information" navigation
- **Expected Result**: All actions function as expected

### 5. Permission and Security Tests

#### Test Case 5.1: Permission Validation
- **Objective**: Verify users without proper permissions cannot perform operations
- **Steps**:
  1. Remove "Company Management GAS" permission set from user
  2. Attempt to access Company Management functionality
- **Expected Result**: Access denied or appropriate error messages

#### Test Case 5.2: Company Information Access
- **Objective**: Verify cross-company data access works correctly
- **Steps**:
  1. Switch between companies
  2. Verify "Keep Company" status is maintained per company
- **Expected Result**: Each company maintains its own protection status

### 6. Error Handling Tests

#### Test Case 6.1: Company Deletion Failure
- **Objective**: Verify graceful handling of deletion failures
- **Steps**:
  1. Create scenario where company deletion might fail (e.g., active sessions)
  2. Attempt deletion
- **Expected Result**: Error is logged, operation continues with other companies

#### Test Case 6.2: Invalid Company Names
- **Objective**: Verify handling of edge cases with company names
- **Steps**:
  1. Test with companies having special characters in names
  2. Test with very long company names
- **Expected Result**: Operations handle edge cases gracefully

### 7. Logging and Audit Tests

#### Test Case 7.1: Operation Logging
- **Objective**: Verify deletion operations are properly logged
- **Steps**:
  1. Perform company deletion operation
  2. Check system logs for audit trail
- **Expected Result**: Start and completion of operations are logged

## Test Data Requirements
- At least 5 test companies with different names
- One company with "CRONUS" in the name
- One company with special characters in the name
- Companies with and without Company Information records

## Success Criteria
- All test cases pass without errors
- User interface is intuitive and responsive
- Safety validations prevent accidental data loss
- Proper error messages are displayed for all error conditions
- Audit trail is maintained for all operations

## Risk Mitigation
- Always test in a non-production environment first
- Ensure database backups are available before testing
- Test with limited number of companies initially
- Verify rollback procedures if needed
