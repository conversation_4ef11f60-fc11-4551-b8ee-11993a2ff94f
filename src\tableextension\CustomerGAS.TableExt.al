tableextension 60006 "Customer GAS" extends Customer
{
    fields
    {
        field(60000; "Location Code GAS"; Code[10])
        {
            Caption = 'Gas Cylinder Location Code';
            DataClassification = CustomerContent;
            TableRelation = Location;
            ToolTip = 'Specifies the location code for gas cylinder transfers for this customer.';
        }
        field(60001; "Max Cylinder Return Time GAS"; DateFormula)
        {
            Caption = 'Maximum Cylinder Return Time';
            DataClassification = CustomerContent;
            ToolTip = 'Specifies the maximum time period the customer can keep gas cylinders before returning them.';
        }
    }
}
