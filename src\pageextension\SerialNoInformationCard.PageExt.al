pageextension 60000 "Serial No. Information Card" extends "Serial No. Information Card"
{
    layout
    {
        addafter(General)
        {
            group("Gas Information GAS")
            {
                Caption = 'Gas Information';

                field("Gas Item No. GAS"; Rec."Gas Item No. GAS")
                {
                    ApplicationArea = All;
                }
                field("Gas Item Description GAS"; Rec."Gas Item Description GAS")
                {
                    ApplicationArea = All;
                }
                field("Gas Quantity GAS"; Rec."Gas Quantity GAS")
                {
                    ApplicationArea = All;
                }
                field("Location Code GAS"; Rec."Location Code GAS")
                {
                    ApplicationArea = All;
                }
                field("Cylinder Type GAS"; Rec."Cylinder Type GAS")
                {
                    ApplicationArea = All;
                }
                field("Cylinder Type Description GAS"; Rec."Cylinder Type Description GAS")
                {
                    ApplicationArea = All;
                }
                field("Product Group GAS"; Rec."Product Group GAS")
                {
                    ApplicationArea = All;
                }
                field("Barcode GAS"; Rec."Barcode GAS")
                {
                    ApplicationArea = All;
                }
                field("Production Date GAS"; Rec."Production Date GAS")
                {
                    ApplicationArea = All;
                }
                field("Independent Serial No. GAS"; Rec."Independent Serial No. GAS")
                {
                    ApplicationArea = All;
                }
            }

            group("Cylinder Specifications GAS")
            {
                Caption = 'Cylinder Specifications';

                field("Color GAS"; Rec."Color GAS")
                {
                    ApplicationArea = All;
                }
                field("Volume GAS"; Rec."Volume GAS")
                {
                    ApplicationArea = All;
                }
                field("Full Weight GAS"; Rec."Full Weight GAS")
                {
                    ApplicationArea = All;
                }
                field("Empty Weight GAS"; Rec."Empty Weight GAS")
                {
                    ApplicationArea = All;
                }
                field("Test Pressure GAS"; Rec."Test Pressure GAS")
                {
                    ApplicationArea = All;
                }
                field("Operating Pressure GAS"; Rec."Operating Pressure GAS")
                {
                    ApplicationArea = All;
                }
                field("Working Pressure GAS"; Rec."Working Pressure GAS")
                {
                    ApplicationArea = All;
                }
                field("Hand Strength GAS"; Rec."Hand Strength GAS")
                {
                    ApplicationArea = All;
                }
            }

            group("Manufacturing Details GAS")
            {
                Caption = 'Manufacturing Details';

                field("Manufacturer GAS"; Rec."Manufacturer GAS")
                {
                    ApplicationArea = All;
                }
                field("Production Number GAS"; Rec."Production Number GAS")
                {
                    ApplicationArea = All;
                }
            }

            group("Ministry Information GAS")
            {
                Caption = 'Ministry Information';

                field("Ministry ID No. GAS"; Rec."Ministry ID No. GAS")
                {
                    ApplicationArea = All;
                }
                field("Ministry Notification Type GAS"; Rec."Ministry Notification Type GAS")
                {
                    ApplicationArea = All;
                }
                field("Ministry Manufacturer Name GAS"; Rec."Ministry Manufacturer Name GAS")
                {
                    ApplicationArea = All;
                }
                field("Ministry Exporter Name GAS"; Rec."Ministry Exporter Name GAS")
                {
                    ApplicationArea = All;
                }
            }

            group("Ownership Information GAS")
            {
                Caption = 'Ownership Information';

                field("Office Name GAS"; Rec."Office Name GAS")
                {
                    ApplicationArea = All;
                }
                field("Cylinder Owner Code GAS"; Rec."Cylinder Owner Code GAS")
                {
                    ApplicationArea = All;
                }
                field("Cylinder Owner Name GAS"; Rec."Cylinder Owner Name GAS")
                {
                    ApplicationArea = All;
                }
            }
        }
    }
}