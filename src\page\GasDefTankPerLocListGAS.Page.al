page 60014 "GasDefTankPerLocList GAS"
{
    ApplicationArea = All;
    Caption = 'Gas Default Tank Per Location List';
    PageType = List;
    SourceTable = "GasDefTankPerLoc GAS";
    UsageCategory = Lists;
    DelayedInsert = true;
    //CardPageId = "GasDefTankPerLocCardGAS"; // Assuming a card page might be needed later

    layout
    {
        area(Content)
        {
            repeater(General)
            {
                field("Location Code"; Rec."Location Code")
                {
                }
                field("Location Name"; Rec."Location Name")
                {
                }
                field("Gas Item No."; Rec."Gas Item No.")
                {
                }
                field("Gas Item Description"; Rec."Gas Item Description")
                {
                }
                field("Default Tank Serial No."; Rec."Default Tank Serial No.")
                {
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            // action(ActionName)
            // {
            //     ApplicationArea = All;
            //     Caption = 'Action Name';
            //     Image = Process;
            //     ToolTip = 'Executes the Action Name action.';

            //     trigger OnAction()
            //     begin

            //     end;
            // }
        }
    }
}
