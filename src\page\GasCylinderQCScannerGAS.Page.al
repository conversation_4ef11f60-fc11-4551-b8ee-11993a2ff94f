page 60008 "Gas Cylinder QC Scanner GAS"
{
    Caption = 'Gas Cylinder QC Scanner';
    PageType = StandardDialog;
    ApplicationArea = All;

    layout
    {
        area(Content)
        {
            group(BarcodeGroup)
            {
                Caption = 'Scan Gas Cylinder for QC';

                field(BarcodeValue; BarcodeValue)
                {
                    Caption = 'Serial Number / Barcode';
                    ToolTip = 'Specifies the barcode/serial number of the gas cylinder for quality control.';
                }
            }
        }
    }

    var
        BarcodeValue: Code[50];

    procedure GetBarcodeValue(): Code[50]
    begin
        exit(BarcodeValue);
    end;

    trigger OnQueryClosePage(CloseAction: Action): Boolean
    var
        SerialNoInformation: Record "Serial No. Information";
        QualityControlManagement: Codeunit "Quality Control Management QCM";
        NoSerialNoInformationFoundErr: Label 'Serial No. Information not found for barcode %1.', Comment = '%1=Serial Number/Barcode';
    begin
        if (CloseAction = Action::OK) and (BarcodeValue <> '') then begin
            SerialNoInformation.SetRange("Serial No.", BarcodeValue);
            if SerialNoInformation.FindFirst() then begin
                QualityControlManagement.CreateQualityControlDocumentFromSerialNoInformation(SerialNoInformation, Enum::"Quality Control Type QCM"::Production, true);
                Message('Production Quality Control document created for Serial No. %1.', BarcodeValue);
            end else
                Error(NoSerialNoInformationFoundErr, BarcodeValue);
        end;
        exit(true);
    end;
}