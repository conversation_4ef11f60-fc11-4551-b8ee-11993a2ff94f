pageextension 60011 "Assembly Order GAS" extends "Assembly Order"
{
    layout
    {
        addlast(content)
        {
            field("Gas Filling Document No. GAS"; Rec."Gas Filling Document No. GAS")
            {
                ApplicationArea = All;
            }
            field("Gas Filling Doc. Line No. GAS"; Rec."Gas Filling Doc. Line No. GAS")
            {
                ApplicationArea = All;
            }
        }
    }

    // actions
    // {
    //     addlast(processing)
    //     {
    //         action("AssignItemTracking GAS")
    //         {
    //             Caption = 'Assign Item Tracking';
    //             ApplicationArea = All;
    //             Image = ItemTrackingLines;
    //             ToolTip = 'Assigns item tracking information to all assembly lines.';

    //             trigger OnAction()
    //             var
    //                 MixedGasManagement: Codeunit "Mixed Gas Management GAS";
    //             begin
    //                 MixedGasManagement.AssignItemTrackingInformationToAssemblyLines(Rec);
    //             end;
    //         }
    //     }
    // }
}
