codeunit 60003 "Mixed Gas Management GAS"
{
    Access = Public;
    procedure CreateAssemblyOrderFromGasCylinderFillingLine(var GasCylinderFillingLine: Record "Gas Cylinder Filling Line GAS")
    var
        AssemblyHeader: Record "Assembly Header";
    begin
        GasCylinderFillingLine.TestField("Assembly Order No.", '');

        AssemblyHeader.Init();
        AssemblyHeader."Document Type" := AssemblyHeader."Document Type"::Order;
        AssemblyHeader.Insert(true);
        AssemblyHeader.Validate("Location Code", GasCylinderFillingLine."Location Code");
        AssemblyHeader.Validate("Item No.", GasCylinderFillingLine."Gas Item No.");
        AssemblyHeader.Validate("Quantity", GasCylinderFillingLine."Quantity");
        AssemblyHeader.Validate("Gas Filling Document No. GAS", GasCylinderFillingLine."Document No.");
        AssemblyHeader.Validate("Gas Filling Doc. Line No. GAS", GasCylinderFillingLine."Line No.");
        AssemblyHeader.Modify(true);
        AssignItemTrackingInformationAssemblyHeaderFromGasCylinderFillingLine(GasCylinderFillingLine, AssemblyHeader);

        AssignItemTrackingInformationToAssemblyLines(AssemblyHeader);

        GasCylinderFillingLine.Validate("Assembly Order No.", AssemblyHeader."No.");
        GasCylinderFillingLine.Modify(true);
    end;

    procedure AssignItemTrackingInformationToAssemblyLines(AssemblyHeader: Record "Assembly Header")
    var
        AssemblyLine: Record "Assembly Line";
    begin
        AssemblyLine.SetRange("Document Type", AssemblyHeader."Document Type");
        AssemblyLine.SetRange("Document No.", AssemblyHeader."No.");
        if not AssemblyLine.FindSet(false) then
            exit;

        repeat
            AssignItemTrackingInformationAssemblyLineFromAssemblyHeader(AssemblyHeader, AssemblyLine);
        until AssemblyLine.Next() = 0;

    end;

    procedure AssignItemTrackingInformationAssemblyHeaderFromGasCylinderFillingLine(GasCylinderFillingLine: Record "Gas Cylinder Filling Line GAS"; AssemblyHeader: Record "Assembly Header")
    var
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        AssemblyHeaderReserve: Codeunit "Assembly Header-Reserve";
        ItemTrackingLines: Page "Item Tracking Lines";
    begin
        AssemblyHeaderReserve.InitFromAsmHeader(TempSourceTrackingSpecification, AssemblyHeader);

        TempTrackingSpecification.Init();
        TempTrackingSpecification."Item No." := GasCylinderFillingLine."Gas Item No.";
#pragma warning disable AA0248
        TempTrackingSpecification.Validate("Package No.", GasCylinderFillingLine."Cylinder Serial No.");
        TempTrackingSpecification.Validate("Quantity (Base)", GasCylinderFillingLine.Quantity);
#pragma warning restore AA0248
        TempTrackingSpecification."Entry No." := TempTrackingSpecification.GetLastEntryNo() + 1;
        TempTrackingSpecification.Insert(false);

        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, AssemblyHeader."Posting Date", TempTrackingSpecification);
    end;

    procedure AssignItemTrackingInformationAssemblyLineFromAssemblyHeader(AssemblyHeader: Record "Assembly Header"; var AssemblyLine: Record "Assembly Line")
    var
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        AssemblyLineReserve: Codeunit "Assembly Line-Reserve";
        ItemTrackingLines: Page "Item Tracking Lines";
    begin
        AssemblyLineReserve.InitFromAsmLine(TempSourceTrackingSpecification, AssemblyLine);

        TempTrackingSpecification.Init();
        TempTrackingSpecification."Item No." := AssemblyLine."No.";
        // Assuming Package No. is coming from the header or related Gas Cylinder Filling Line
#pragma warning disable AA0248
        TempTrackingSpecification.Validate("Package No.", GetPackageNoFromAssemblyHeader(AssemblyHeader, AssemblyLine));
        TempTrackingSpecification.Validate("Quantity (Base)", AssemblyLine."Quantity (Base)");
#pragma warning restore AA0248
        TempTrackingSpecification."Entry No." := TempTrackingSpecification.GetLastEntryNo() + 1;
        TempTrackingSpecification.Insert(false);

        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, AssemblyHeader."Posting Date", TempTrackingSpecification);
    end;

    local procedure GetPackageNoFromAssemblyHeader(AssemblyHeader: Record "Assembly Header"; AssemblyLine: Record "Assembly Line"): Code[50]
    var
        GasDefTankPerLoc: Record "GasDefTankPerLoc GAS";
    begin
        GasDefTankPerLoc.SetLoadFields("Default Tank Serial No.");
        if GasDefTankPerLoc.Get(AssemblyHeader."Location Code", AssemblyLine."No.") then
            exit(GasDefTankPerLoc."Default Tank Serial No.");
    end;
}
