tableextension 60004 "Sales Shipment Line GAS" extends "Sales Shipment Line"
{
    fields
    {
        field(60000; "Gas Cylinder Item No. GAS"; Code[20])
        {
            Caption = 'Gas Cylinder Item No.';
            DataClassification = CustomerContent;
            TableRelation = Item where(Type = const(Inventory));
            ToolTip = 'Specifies the gas cylinder item number.';
            AllowInCustomizations = Always;
        }
        field(60001; "Gas Cylinder Quantity GAS"; Decimal)
        {
            Caption = 'Gas Cylinder Quantity';
            DataClassification = CustomerContent;
            DecimalPlaces = 0 : 5;
            ToolTip = 'Specifies the gas cylinder quantity.';
            AllowInCustomizations = Always;
        }
    }
}