table 60007 "Cylinder Entry/Exit Line GAS"
{
    Caption = 'Cylinder Entry/Exit Line';
    DataClassification = CustomerContent;
    // Add LookupPageId and DrillDownPageId when related pages are created

    fields
    {
        field(1; "Document No."; Code[20])
        {
            Caption = 'Document No.';
            TableRelation = "Cylinder Entry/Exit Header GAS";
            AllowInCustomizations = Always;
        }
        field(2; "Line No."; Integer)
        {
            Caption = 'Line No.';
            ToolTip = 'Specifies the line number.';
        }
        field(3; "Entry/Exit Type"; Enum "Cylinder Entry/Exit Type GAS")
        {
            Caption = 'Entry/Exit Type';
            ToolTip = 'Specifies whether the line is for delivery or receive.';
        }
        field(4; "Cylinder Serial No."; Code[50])
        {
            Caption = 'Cylinder Serial No.';
            TableRelation = "Serial No. Information"."Serial No.";
            ToolTip = 'Specifies the serial number of the gas cylinder.';
        }
        field(5; "Cylinder Item No."; Code[20])
        {
            Caption = 'Cylinder Item No.';
            TableRelation = Item;
            ToolTip = 'Specifies the item number of the gas cylinder.';
        }
        field(6; "Cylinder Item Description"; Text[100])
        {
            Caption = 'Cylinder Item Description';
            Editable = false;
            ToolTip = 'Specifies the description of the gas cylinder item.';
        }
        field(7; "Gas Item No."; Code[20])
        {
            Caption = 'Gas Item No.';
            TableRelation = Item;
            ToolTip = 'Specifies the gas item number to be filled into the cylinder.';
        }
        field(8; "Gas Item Description"; Text[100])
        {
            Caption = 'Gas Item Description';
            Editable = false;
            ToolTip = 'Specifies the description of the gas item.';
        }
        field(9; "Gas Quantity"; Decimal)
        {
            Caption = 'Gas Quantity';
            DecimalPlaces = 0 : 5;
            MinValue = 0;
            ToolTip = 'Specifies the quantity of gas to fill in the cylinder.';
        }
        field(10; "Read Cylinder Barcode"; Text[100])
        {
            Caption = 'Read Cylinder Barcode';
            ToolTip = 'Scan or enter the barcode of the cylinder.';
        }
        field(11; "Warehouse Shipment No."; Code[20])
        {
            Caption = 'Warehouse Shipment No.';
            ToolTip = 'Specifies the related warehouse shipment number.';
        }
        field(12; "Posted Warehouse Shipment No."; Code[20])
        {
            Caption = 'Posted Warehouse Shipment No.';
            ToolTip = 'Specifies the related posted warehouse shipment number.';
        }
        field(13; "Transfer Order No."; Code[20])
        {
            Caption = 'Transfer Order No.';
            ToolTip = 'Specifies the related transfer order number.';
        }
    }

    keys
    {
        key(PK; "Document No.", "Line No.")
        {
            Clustered = true;
        }
    }

    trigger OnInsert()
    var
        LineRec: Record "Cylinder Entry/Exit Line GAS";
        LastLineNo: Integer;
    begin
        LineRec.SetRange("Document No.", "Document No.");
        if LineRec.FindLast() then
            LastLineNo := LineRec."Line No."
        else
            LastLineNo := 0;
        "Line No." := LastLineNo + 10000;
    end;
}