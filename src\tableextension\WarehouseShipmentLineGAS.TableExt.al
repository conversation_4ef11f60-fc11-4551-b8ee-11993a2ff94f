tableextension 60008 "Warehouse Shipment Line GAS" extends "Warehouse Shipment Line"
{
    fields
    {
        field(60000; "Total Tracked Qty. GAS"; Decimal)
        {
            Caption = 'Total Item Tracking Qty.';
            ToolTip = 'Specifies the total quantity of item tracking assigned to the warehouse shipment line.';
            DecimalPlaces = 0 : 5;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Reservation Entry"."Quantity (Base)" where("Source Type" = field("Source Type"),
                                                                        "Source Subtype" = field("Source Subtype"),
                                                                        "Source ID" = field("Source No."),
                                                                        "Source Ref. No." = field("Source Line No."),
                                                                        "Reservation Status" = const(Surplus)));
        }
    }
}
