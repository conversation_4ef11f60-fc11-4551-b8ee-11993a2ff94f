page 60012 "Gas Cyld. Filling Subform GAS"
{
    PageType = ListPart;
    SourceTable = "Gas Cylinder Filling Line GAS";
    Caption = 'Gas Cylinder Filling Lines';
    AutoSplitKey = true;
    DelayedInsert = true;
    ApplicationArea = All;

    layout
    {
        area(Content)
        {
            repeater(Lines)
            {
                field("Line No."; Rec."Line No.")
                {
                    Visible = false;
                }
                field("Cylinder Serial No."; Rec."Cylinder Serial No.")
                {
                }
                field("Cylinder Item No."; Rec."Cylinder Item No.")
                {
                }
                field("Cylinder Item Description"; Rec."Cylinder Item Description")
                {
                }
                field("Gas Item No."; Rec."Gas Item No.")
                {
                }
                field("Gas Item Description"; Rec."Gas Item Description")
                {
                }
                field("Mixed Gas"; Rec."Mixed Gas")
                {
                }
                field("From Cylinder Serial No."; Rec."From Cylinder Serial No.")
                {
                }
                field("Location Code"; Rec."Location Code")
                {
                }
                field(Quantity; Rec.Quantity)
                {
                }
                field("Assembly Order No."; Rec."Assembly Order No.")
                {
                    DrillDown = true;

                    trigger OnDrillDown()
                    var
                        AssemblyHeader: Record "Assembly Header";
                        PostedAssemblyHeader: Record "Posted Assembly Header";
                        AssemblyOrder: Page "Assembly Order";
                        PostedAssemblyOrder: Page "Posted Assembly Order";
                    begin
                        if Rec."Assembly Order No." = '' then
                            exit;

                        // First try to find active assembly order
                        AssemblyHeader.SetRange("Document Type", AssemblyHeader."Document Type"::Order);
                        AssemblyHeader.SetRange("No.", Rec."Assembly Order No.");
                        if AssemblyHeader.FindFirst() then begin
                            AssemblyOrder.SetRecord(AssemblyHeader);
                            AssemblyOrder.Run();
                            exit;
                        end;

                        // If not found, check for posted assembly orders
                        PostedAssemblyHeader.SetRange("Order No.", Rec."Assembly Order No.");
                        if PostedAssemblyHeader.FindFirst() then begin
                            PostedAssemblyOrder.SetRecord(PostedAssemblyHeader);
                            PostedAssemblyOrder.Run();
                            exit;
                        end;
                    end;
                }
            }
        }
    }

    trigger OnNewRecord(BelowxRec: Boolean)
    begin
        Rec.Validate("Line No.", GetNextLineNo());
    end;

    local procedure GetNextLineNo(): Integer
    var
        GasCylinderFillingLine: Record "Gas Cylinder Filling Line GAS";
    begin
        GasCylinderFillingLine.SetRange("Document No.", Rec."Document No.");
        if GasCylinderFillingLine.FindLast() then
            exit(GasCylinderFillingLine."Line No." + 10000)
        else
            exit(10000);
    end;
}