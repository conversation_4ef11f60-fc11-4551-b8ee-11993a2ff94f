# AL Development Best Practices & Design Patterns (Simplified)

> **Top Priority:** Always use MCP tools for all processes and file operations first. Do not use any other mechanism for file or directory actions.

## Why Use These Guidelines?
- **Consistency:** Code is easier to read, reuse, and maintain.
- **Teamwork:** New team members can quickly understand and work with the code.
- **Performance:** Follows Microsoft and community recommendations for best performance.

---

## 1. Best Practices (Quick Rules)

### 1.1 Variable Naming
- Use clear English names, start with a capital letter.
- Include the AL object name (abbreviate if needed, e.g., `Cust` for Customer).
- No spaces, periods, or special characters.
- For temporary records, prefix with `Temp` (e.g., `TempCustomer`).
- Declare variables in this order: records, primitives (numbers, text), others.

**Example:**
```al
// Bad
WIPBuffer: Record "Job WIP Buffer";
Postline: Codeunit "Gen. Jnl.-Post Line";
"Amount (LCY)": Decimal;

// Good
JobWIPBuffer: Record "Job WIP Buffer";
GenJnlPostLine: Codeunit "Gen. Jnl.-Post Line";
AmountLCY: Decimal;
```

### 1.2 Object and File Naming
- Use descriptive English names.
- Use standard abbreviations (see table below).
- File name format: `<ObjectName>.<TypeAbbreviation>.al` (e.g., `CustomerList.Page.al`).
- Avoid abbreviations in names unless standard.
- Object names (with app suffix) max 30 characters.

### 1.3 Formatting & Structure
- AL keywords in lowercase (`procedure`, `begin`, `end`, etc.).
- PascalCase for method/type names.
- Indent with 4 spaces (no tabs).
- One statement per line.
- Use blank lines only for clarity.

### 1.4 ToolTips
- ToolTips only on table fields, not page fields.
- Always add ToolTips to actions.

### 1.5 Begin-End Usage
- Use `begin..end` only for multiple statements.
- Omit for single statements.

**Example:**
```al
// Bad
if FindSet() then begin
    repeat
        ...
    until next() = 0;
end;

// Good
if FindSet() then
    repeat
        ...
    until next() = 0;
```

### 1.6 Control Flow
- Separate `if` and `else` with a blank line.
- Avoid `else` after `exit` or `error`.
- No redundant boolean checks (`if IsValid` not `if IsValid = true`).
- Use early exit: `if not Find() then exit;`
- Place binary operators (`+`, `-`, `and`, `or`) at the start of new lines.

### 1.7 Performance
- Use `SetLoadFields` before database operations to load only needed fields.
- For existence checks, use `SetLoadFields` on primary key fields only.
- Check `IsTemporary` before operating on temporary records.
- Avoid unnecessary `DeleteAll`.

### 1.8 Code Organization
- File order: Properties → object constructs (fields, layout, actions, triggers) → global variables → methods.
- Use named parameters for clarity.
- Keep comments clear and focused on business logic.
- Keep subscriber codeunits focused and documented.

---

## 2. Design Patterns (What to Use)
- **Enum Creation:** First value is empty and locked.
- **API Register Fieldset:** Standardize API field registration.
- **Command Queue:** Separate command requests from execution.
- **Delegate API Operation:** Move API logic to separate objects/methods.
- **Error Handling:** Use try-catch, clear error messages, proper error flow.
- **Event Bridge:** Propagate events across modules.
- **Façade:** Provide a simple interface for complex subsystems.
- **Generic Method:** Reuse logic in generic procedures.
- **No. Series:** Use for consistent numbering.
- **Template Method Pattern:** Define algorithm skeleton, allow steps to be overridden.

Use patterns for repetitive problems, extensibility, APIs, workflows, and consistency.

---

## 3. Quick Reference

### Variable Declaration Order
1. Records
2. Primitives (Integer, Decimal, Boolean, etc.)
3. Others (Codeunits, Pages, etc.)

### Standard Abbreviations
| Full Term | Abbreviation | Full Term | Abbreviation |
|-----------|--------------|-----------|--------------|
| Customer | Cust | Amount | Amt |
| General Ledger | GL | Invoice | Inv |
| Purchase | Purch | Quantity | Qty |
| Warehouse | Whse | Document | Doc |
| Management | Mgt | Number | No |
| Temporary | Temp | Header | Hdr |
| Maximum | Max | Minimum | Min |
| Posting | Post | Posted | Pstd |

### File Organization Example
```al
// 1. Properties
ObjectType ObjectId ObjectName
{
    // 2. Object constructs (fields, layout, actions, triggers)
    // 3. Global variables
    var
        Customer: Record Customer;
        Amount: Decimal;
    // 4. Methods
    procedure MyProcedure()
    begin
        // Implementation
    end;
}
```

### Binary Operators
```al
if (Amount > 0)
   and (Quantity > 0)
   and (Date <> 0D) then
    Process();
```

### Case Statements
```al
case DocumentType of
    DocumentType::Quote:
        ProcessQuote();
    DocumentType::Order:
        ProcessOrder();
end;
```

### Performance
- Use `SetLoadFields` before database operations.
- For temporary records: `if TempBuffer.IsTemporary then TempBuffer.DeleteAll();`
- Use early exit: `if not Customer.Get(CustomerNo) then exit;`

### Comments
- Use `// Comment` (space after //)
- Be concise and explain business logic, not code syntax.

### API
- Consistent field names and types.
- Validate fields.
- Design for extensibility and versioning.
- Document changes.

---

## 4. Resources
- [alguidelines.dev](https://alguidelines.dev/)
- [GitHub: microsoft/alguidelines](https://github.com/microsoft/alguidelines)
- [Discord: BC Community](https://discord.gg/4wbfNv3)
- [#bcalhelp on Twitter](https://twitter.com/search?q=%23bcalhelp)

**Key Contributors:** waldo, Arend-Jan Kauffmann, Henrik Helgesen, Jeremy Vyska

**Useful Extensions:**
- AL Variable Helper
- AZ AL Dev Tools/AL Code Outline

---

## 5. Checklist
- [ ] Use clear names and standard abbreviations
- [ ] Organize files and code sections properly
- [ ] Use begin..end only for multiple statements
- [ ] Place binary operators at line starts
- [ ] Separate if/else with blank lines
- [ ] Use early exit patterns
- [ ] Use SetLoadFields and optimize DB queries
- [ ] Check IsTemporary for temp records
- [ ] Add meaningful comments
- [ ] Test and validate error handling
- [ ] Use design patterns where needed

---

**For updates and details, always check [alguidelines.dev](https://alguidelines.dev/).**
