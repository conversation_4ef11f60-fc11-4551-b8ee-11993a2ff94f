tableextension 60001 "Sales Line GAS" extends "Sales Line"
{
    fields
    {
        field(60000; "Gas Cylinder Item No. GAS"; Code[20])
        {
            Caption = 'Gas Cylinder Item No.';
            DataClassification = CustomerContent;
            TableRelation = Item;
            ToolTip = 'Specifies the gas cylinder item number.';

            trigger OnValidate()
            var
                CylinderItem: Record Item;
            begin
                if Rec."Gas Cylinder Item No. GAS" <> '' then begin
                    CylinderItem.Get(Rec."Gas Cylinder Item No. GAS");

                    // Test if Gas Item No. exists and throw error if it doesn't
                    // CylinderItem.TestField("Gas Item No. GAS");

                    // Rec.Validate("No.", CylinderItem."Gas Item No. GAS");
                    Rec.Description := CylinderItem.Description;
                    Rec."Gas Cylinder Item No. GAS" := CylinderItem."No.";
                end;
            end;
        }
        field(60001; "Gas Cylinder Quantity GAS"; Decimal)
        {
            Caption = 'Gas Cylinder Quantity';
            DataClassification = CustomerContent;
            DecimalPlaces = 0 : 5;
            ToolTip = 'Specifies the gas cylinder quantity.';

            trigger OnValidate()
            var
                CylinderItem: Record Item;
            begin
                if (Rec."Gas Cylinder Quantity GAS" <> 0) and (Rec."Gas Cylinder Item No. GAS" <> '') then begin
                    CylinderItem.Get(Rec."Gas Cylinder Item No. GAS");

                    // Calculate Quantity based on cylinder quantity and unit volume
                    Rec.Validate(Quantity, Rec."Gas Cylinder Quantity GAS" * CylinderItem."Unit Volume");
                end;
            end;
        }
    }
}