page 60010 "Gas Cylinder Filling List GAS"
{
    PageType = List;
    ApplicationArea = All;
    UsageCategory = Lists;
    SourceTable = "Gas Cyld. Filling Header GAS";
    Caption = 'Gas Cylinder Filling Documents';
    CardPageId = "Gas Cylinder Filling Card GAS";
    Editable = false;

    layout
    {
        area(Content)
        {
            repeater(GroupName)
            {
                field("No."; Rec."No.")
                {
                }
                field("Document Date"; Rec."Document Date")
                {
                }
                field("Posting Date"; Rec."Posting Date")
                {
                }
                field(Status; Rec.Status)
                {
                    StyleExpr = StatusStyleTxt;
                }
                field("Total Quantity"; Rec."Total Quantity")
                {
                }
                field("No. of Cylinders"; Rec."No. of Cylinders")
                {
                }
                field("User ID"; Rec."User ID")
                {
                }
            }
        }
        area(FactBoxes)
        {
            systempart(Links; Links)
            {
                ApplicationArea = RecordLinks;
            }
            systempart(Notes; Notes)
            {
                ApplicationArea = Notes;
            }
        }
    }

    actions
    {
        area(Navigation)
        {
            action(Card)
            {
                Caption = 'Card';
                Image = EditLines;
                RunObject = page "Gas Cylinder Filling Card GAS";
                RunPageLink = "No." = field("No.");
                ShortcutKey = 'Shift+F7';
                ToolTip = 'View or edit detailed information for the record.';
            }
        }
        area(Processing)
        {
            action(Release)
            {
                Caption = 'Release';
                Image = ReleaseDoc;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ShortcutKey = 'Ctrl+F9';
                ToolTip = 'Release the document for the next step in processing.';

                trigger OnAction()
                var
                    GasCylinderFillingMgt: Codeunit "Gas Cylinder Filling Mgt. GAS";
                begin
                    GasCylinderFillingMgt.ReleaseDocument(Rec);
                end;
            }
            action(Reopen)
            {
                Caption = 'Reopen';
                Image = ReOpen;
                Promoted = true;
                PromotedCategory = Process;
                PromotedOnly = true;
                ToolTip = 'Reopen the document for editing.';

                trigger OnAction()
                var
                    GasCylinderFillingMgt: Codeunit "Gas Cylinder Filling Mgt. GAS";
                begin
                    GasCylinderFillingMgt.ReopenDocument(Rec);
                end;
            }
            action(Post)
            {
                Caption = 'Post';
                Image = PostDocument;
                Promoted = true;
                PromotedCategory = Process;
                PromotedIsBig = true;
                PromotedOnly = true;
                ShortcutKey = 'F9';
                ToolTip = 'Post the document to update cylinder gas item associations.';

                trigger OnAction()
                var
                    GasCylinderFillingMgt: Codeunit "Gas Cylinder Filling Mgt. GAS";
                begin
                    GasCylinderFillingMgt.PostDocument(Rec);
                end;
            }
        }
    }

    var
        StatusStyleTxt: Text;

    trigger OnAfterGetRecord()
    begin
        StyleStatusText();
    end;

    local procedure StyleStatusText()
    begin
        StatusStyleTxt := '';

        case Rec.Status of
            Rec.Status::Open:
                StatusStyleTxt := Format(PageStyle::Standard);
            Rec.Status::Released:
                StatusStyleTxt := Format(PageStyle::Favorable);
            Rec.Status::Posted:
                StatusStyleTxt := Format(PageStyle::Subordinate);
        end;
    end;
}