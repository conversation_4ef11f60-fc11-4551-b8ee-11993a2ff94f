page 60016 "Cylinder Entry/Exit Header GAS"
{
    PageType = Card;
    SourceTable = "Cylinder Entry/Exit Header GAS";
    Caption = 'Cylinder Entry/Exit Header';
    UsageCategory = Documents;
    ApplicationArea = All;
    Extensible = false;

    layout
    {
        area(Content)
        {
            group(General)
            {
                Caption = 'General';
                field("No."; Rec."No.")
                {
                    Caption = 'No.';
                }
                field("Document Date"; Rec."Document Date")
                {
                    Caption = 'Document Date';
                }
                field("Posting Date"; Rec."Posting Date")
                {
                    Caption = 'Posting Date';
                }
                field("No. Series"; Rec."No. Series")
                {
                    Caption = 'No. Series';
                }
                field("Customer No."; Rec."Customer No.")
                {
                    Caption = 'Customer No.';
                }
                field("Customer Name"; Rec."Customer Name")
                {
                    Caption = 'Customer Name';
                }
                field("Ship-to Code"; Rec."Ship-to Code")
                {
                    Caption = 'Ship-to Code';
                }
                field("Ship-to Name"; Rec."Ship-to Name")
                {
                    Caption = 'Ship-to Name';
                }
                field("Truck Plate"; Rec."Truck Plate")
                {
                    Caption = 'Truck Plate';
                }
                field("Entry/Exit Type"; Rec."Entry/Exit Type")
                {
                    Caption = 'Entry/Exit Type';
                }
                field("Total Delivery Gas Quantity"; Rec."Total Delivery Gas Quantity")
                {
                    Caption = 'Total Delivery Gas Quantity';
                }
                field("Total Delivery Cylinder Count"; Rec."Total Delivery Cylinder Count")
                {
                    Caption = 'Total Delivery Cylinder Count';
                }
            }
            part(Lines; "CylinderEntryExitLineSubform")
            {
                SubPageLink = "Document No." = field("No.");
                Caption = 'Lines';
            }
        }
    }
}