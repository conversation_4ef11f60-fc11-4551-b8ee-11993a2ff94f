table 60005 "Gas Cylinder Cue GAS"
{
    Caption = 'Gas Cylinder Cue';
    DataClassification = CustomerContent;

    fields
    {
        field(1; "Primary Key"; Code[10])
        {
            Caption = 'Primary Key';
            DataClassification = SystemMetadata;
            NotBlank = false; // Added NotBlank property
            AllowInCustomizations = Never;
        }
        // Add other fields here if you want to display data-driven cues
        // For example:
        // field(10; "Open Production QCs"; Integer)
        // {
        //     Caption = 'Open Production QCs';
        //     FieldClass = FlowField;
        //     CalcFormula = count("Quality Control QCM" where("Source Type" = const("Gas Cylinder"), Status = const(Open)));
        // }
    }

    keys
    {
        key(PK; "Primary Key")
        {
            Clustered = true;
        }
    }
}