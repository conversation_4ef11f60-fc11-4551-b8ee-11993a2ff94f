table 60006 "Cylinder Entry/Exit Header GAS"
{
    Caption = 'Cylinder Entry/Exit Header';
    DataClassification = CustomerContent;
    LookupPageId = "Cylinder Entry/Exit List GAS";
    DrillDownPageId = "Cylinder Entry/Exit List GAS";

    fields
    {
        field(1; "No."; Code[20])
        {
            Caption = 'No.';
            ToolTip = 'Specifies the number of the cylinder entry/exit document.';
        }
        field(2; "Document Date"; Date)
        {
            Caption = 'Document Date';
            ToolTip = 'Specifies the date when the document was created.';
        }
        field(3; "Posting Date"; Date)
        {
            Caption = 'Posting Date';
            ToolTip = 'Specifies the date when the document will be posted.';
        }
        field(4; "No. Series"; Code[20])
        {
            Caption = 'No. Series';
            Editable = false;
            TableRelation = "No. Series";
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the number series used.';
        }
        field(5; "Customer No."; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = Customer;
            ToolTip = 'Specifies the customer number.';
            trigger OnValidate()
            var
                Cust: Record Customer;
            begin
                if "Customer No." <> '' then begin
                    if Cust.Get("Customer No.") then
                        "Customer Name" := Cust.Name
                    else
                        "Customer Name" := '';
                end else
                    "Customer Name" := '';
            end;
        }
        field(6; "Customer Name"; Text[100])
        {
            Caption = 'Customer Name';
            Editable = false;
            ToolTip = 'Specifies the customer name.';
        }
        field(7; "Ship-to Code"; Code[10])
        {
            Caption = 'Ship-to Code';
            TableRelation = "Ship-to Address".Code where("Customer No." = field("Customer No."));
            ToolTip = 'Specifies the ship-to code.';
            trigger OnValidate()
            var
                ShipTo: Record "Ship-to Address";
            begin
                if ("Customer No." <> '') and ("Ship-to Code" <> '') then begin
                    if ShipTo.Get("Customer No.", "Ship-to Code") then
                        "Ship-to Name" := ShipTo.Name
                    else
                        "Ship-to Name" := '';
                end else
                    "Ship-to Name" := '';
            end;
        }
        field(8; "Ship-to Name"; Text[100])
        {
            Caption = 'Ship-to Name';
            Editable = false;
            ToolTip = 'Specifies the ship-to name.';
        }
        field(9; "Entry/Exit Type"; Enum "Cylinder Entry/Exit Type GAS")
        {
            Caption = 'Entry/Exit Type';
            ToolTip = 'Specifies whether the document is for delivery or receive.';
            InitValue = Delivery;
        }
        field(10; Barcode; Code[50])
        {
            Caption = 'Barcode';
            ToolTip = 'Specifies the barcode for the document.';
        }
        field(11; "Total Delivery Gas Quantity"; Decimal)
        {
            Caption = 'Total Delivery Gas Quantity';
            Editable = false;
            ToolTip = 'Shows the sum of Gas Quantity for all Delivery lines.';
            FieldClass = FlowField;
            CalcFormula = sum("Cylinder Entry/Exit Line GAS"."Gas Quantity" where("Document No." = field("No."), "Entry/Exit Type" = const(Delivery)));
        }
        field(12; "Total Delivery Cylinder Count"; Integer)
        {
            Caption = 'Total Delivery Cylinder Count';
            Editable = false;
            ToolTip = 'Shows the number of Delivery lines.';
            FieldClass = FlowField;
            CalcFormula = count("Cylinder Entry/Exit Line GAS" where("Document No." = field("No."), "Entry/Exit Type" = const(Delivery)));
        }
        field(13; "Truck Plate"; Code[20])
        {
            Caption = 'Truck Plate';
            TableRelation = Location.Code where("Truck GAS" = const(true));
            ToolTip = 'Specifies the truck plate by selecting a location where Truck is true.';
        }
    }

    keys
    {
        key(PK; "No.")
        {
            Clustered = true;
        }
        key(Key2; "Posting Date") { }
    }

    trigger OnInsert()
    var
        CylinderMgtSetup: Record "Gas Cyl. Mgt. Setup GAS";
        NoSeries: Codeunit "No. Series";
    begin
        if "No." = '' then begin
            CylinderMgtSetup.Get();
            CylinderMgtSetup.TestField("Cylinder Entry/Exit Nos. GAS");
            "No. Series" := CylinderMgtSetup."Cylinder Entry/Exit Nos. GAS";
            if NoSeries.AreRelated(CylinderMgtSetup."Cylinder Entry/Exit Nos. GAS", xRec."No. Series") then
                "No. Series" := xRec."No. Series";
            "No." := NoSeries.GetNextNo("No. Series");
        end;

        "Document Date" := WorkDate();
        "Posting Date" := WorkDate();
    end;

}