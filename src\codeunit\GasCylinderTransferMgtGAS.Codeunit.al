codeunit 60001 "Gas Cylinder Transfer Mgt. GAS"
{
    procedure AssignItemTrackingToTransferLine(TransferLine: Record "Transfer Line"; TrackingNo: Code[50]; QtyToAssign: Decimal)
    var
        TempTrackingSpecification: Record "Tracking Specification" temporary;
        TempSourceTrackingSpecification: Record "Tracking Specification" temporary;
        Item: Record Item;
        ItemTrackingCode: Record "Item Tracking Code";
        TransferLineReserve: Codeunit "Transfer Line-Reserve";
        ItemTrackingLines: Page "Item Tracking Lines";
        UseSerialTracking: Boolean;
        UsePackageTracking: Boolean;
    begin
        // Get item tracking setup to determine which tracking type to use
        if not Item.Get(TransferLine."Item No.") then
            exit;

        // Check item tracking code settings
        UseSerialTracking := false;
        UsePackageTracking := false;
        if Item."Item Tracking Code" <> '' then
            if ItemTrackingCode.Get(Item."Item Tracking Code") then begin
                UseSerialTracking := ItemTrackingCode."SN Specific Tracking";
                UsePackageTracking := ItemTrackingCode."Package Specific Tracking";
            end;

        // Initialize tracking specification from transfer line
        TransferLineReserve.InitFromTransLine(TempSourceTrackingSpecification, TransferLine, TransferLine."Shipment Date", Enum::"Transfer Direction"::Outbound);

        // Set up temporary tracking specification
        TempTrackingSpecification.Init();
        TempTrackingSpecification."Item No." := TransferLine."Item No.";
        if UseSerialTracking then begin
#pragma warning disable LC0078
            TempTrackingSpecification.Validate("Serial No.", TrackingNo);
            TempTrackingSpecification."New Serial No." := TrackingNo;
        end else
            if UsePackageTracking then begin
                TempTrackingSpecification.Validate("Package No.", TrackingNo);
                TempTrackingSpecification."New Package No." := TrackingNo;
            end;
        TempTrackingSpecification."Quantity Handled (Base)" := 0;
        TempTrackingSpecification.Validate("Quantity (Base)", QtyToAssign);
#pragma warning restore LC0078
        TempTrackingSpecification."Entry No." += 1;
        TempTrackingSpecification.Insert(false);

        // Register tracking using the Item Tracking Lines page
        ItemTrackingLines.SetRunMode(Enum::"Item Tracking Run Mode"::Transfer);
        ItemTrackingLines.RegisterItemTrackingLines(TempSourceTrackingSpecification, TransferLine."Shipment Date", TempTrackingSpecification);
    end;

    procedure ReadGasCylinderBarcodeForTransfer(TransferHeader: Record "Transfer Header")
    var
        BarcodePage: Page "Gas Cylinder Barcode GAS";
        SerialNo: Code[50];
    begin
        BarcodePage.LookupMode(true);
        if BarcodePage.RunModal() = Action::LookupOK then begin
            SerialNo := BarcodePage.GetBarcodeValue();
            if SerialNo <> '' then
                ProcessScannedCylinder(SerialNo, TransferHeader);
        end;
    end;

    procedure ProcessScannedCylinderForTransfer(SerialNo: Code[50]; TransferHeader: Record "Transfer Header")
    begin
        ProcessScannedCylinder(SerialNo, TransferHeader);
    end;

    local procedure ProcessScannedCylinder(SerialNo: Code[50]; TransferHeader: Record "Transfer Header")
    var
        SerialNoInfo: Record "Serial No. Information";
        CylinderItem: Record Item;
        GasItem: Record Item;
        TransferLine: Record "Transfer Line";
        CylinderLineNo: Integer;
        GasLineNo: Integer;
    begin        // Find the serial number
        SerialNoInfo.SetLoadFields("Serial No.", "Item No.", "Gas Item No. GAS");
        SerialNoInfo.SetRange("Serial No.", SerialNo);
        if not SerialNoInfo.FindFirst() then
            Error(GasCylinderNotFoundLbl, SerialNo);
        SerialNoInfo.CalcFields("Gas Quantity GAS");
        // Get the cylinder item
        CylinderItem.SetLoadFields("No.", Description);
        if not CylinderItem.Get(SerialNoInfo."Item No.") then
            Error(CylinderItemNotFoundLbl, SerialNoInfo."Item No.");

        // Check if this is a cylinder with gas item association
        if SerialNoInfo."Gas Item No. GAS" = '' then
            Error(NoGasItemAssociatedLbl, SerialNo);

        // Get the gas item
        if not GasItem.Get(SerialNoInfo."Gas Item No. GAS") then
            Error(GasItemNotFoundLbl, SerialNoInfo."Gas Item No. GAS");

        // Determine line numbers for new lines
        TransferLine.SetRange("Document No.", TransferHeader."No.");
        if TransferLine.FindLast() then
            CylinderLineNo := TransferLine."Line No." + 10000
        else
            CylinderLineNo := 10000;
        GasLineNo := CylinderLineNo + 10000;

        // Check if cylinder item already exists in the transfer
        TransferLine.Reset();
        TransferLine.SetRange("Document No.", TransferHeader."No.");
        TransferLine.SetRange("Item No.", CylinderItem."No.");
        if TransferLine.FindFirst() then begin
            // Update existing line
            TransferLine.Validate(Quantity, TransferLine.Quantity + 1);
            TransferLine.Modify(true);
            CylinderLineNo := TransferLine."Line No.";
        end else
            // Create new line for cylinder
            CreateTransferLine(TransferHeader."No.", CylinderLineNo, CylinderItem."No.", 1);

        // Refresh to get the transfer line with the correct properties
        TransferLine.Reset();
        TransferLine.SetRange("Document No.", TransferHeader."No.");
        TransferLine.SetRange("Line No.", CylinderLineNo);
        if TransferLine.FindFirst() then
            AssignItemTrackingToTransferLine(TransferLine, SerialNo, 1);

        // Check if gas item already exists in the transfer
        TransferLine.Reset();
        TransferLine.SetRange("Document No.", TransferHeader."No.");
        TransferLine.SetRange("Item No.", GasItem."No.");
        if TransferLine.FindFirst() then begin
            // Update existing line
            TransferLine.Validate(Quantity, TransferLine.Quantity + SerialNoInfo."Gas Quantity GAS");
            TransferLine.Modify(true);
            GasLineNo := TransferLine."Line No.";
        end else
            if SerialNoInfo."Gas Quantity GAS" > 0 then
                // Create new line for gas
                CreateTransferLine(TransferHeader."No.", GasLineNo, GasItem."No.", SerialNoInfo."Gas Quantity GAS");

        // Assign package tracking to gas item
        if SerialNoInfo."Gas Quantity GAS" > 0 then begin
            TransferLine.Reset();
            TransferLine.SetRange("Document No.", TransferHeader."No.");
            TransferLine.SetRange("Line No.", GasLineNo);
            if TransferLine.FindFirst() then
                AssignItemTrackingToTransferLine(TransferLine, SerialNo, SerialNoInfo."Gas Quantity GAS");
        end;

        Message(GasCylinderAddedLbl, SerialNo, GasItem."No.");
    end;

    local procedure CreateTransferLine(DocNo: Code[20]; LineNo: Integer; ItemNo: Code[20]; Qty: Decimal)
    var
        NewTransferLine: Record "Transfer Line";
        Item: Record Item;
    begin
        Item.Get(ItemNo);

        NewTransferLine.Init();
        NewTransferLine."Document No." := DocNo;
        NewTransferLine."Line No." := LineNo;
        NewTransferLine.Insert(true);
        NewTransferLine.Validate("Item No.", ItemNo);
        NewTransferLine.Validate(Quantity, Qty);
        NewTransferLine.Modify(true);
    end;

    var
        GasCylinderNotFoundLbl: Label 'Gas cylinder with serial number %1 not found.', Comment = '%1 = Serial Number';
        CylinderItemNotFoundLbl: Label 'Cylinder item %1 not found.', Comment = '%1 = Item No.';
        NoGasItemAssociatedLbl: Label 'No gas item associated with cylinder serial number %1.', Comment = '%1 = Serial Number';
        GasItemNotFoundLbl: Label 'Gas item %1 not found.', Comment = '%1 = Gas Item No.';
        GasCylinderAddedLbl: Label 'Gas cylinder %1 with gas item %2 added to the transfer order.', Comment = '%1 = Serial Number, %2 = Gas Item No.';
}