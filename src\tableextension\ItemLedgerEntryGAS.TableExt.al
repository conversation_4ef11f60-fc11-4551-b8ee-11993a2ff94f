tableextension 60007 "Item Ledger Entry GAS" extends "Item Ledger Entry"
{
    fields
    {
        field(60000; "Consignment Customer No. GAS"; Code[20])
        {
            Caption = 'Consignment Customer No.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Location."Consignment Customer No. INF" where(Code = field("Location Code")));
            ToolTip = 'Specifies the consignment customer number associated with this location.';
        }
        field(60002; "Consignment Ship-to Code GAS"; Code[10])
        {
            Caption = 'Consignment Ship-to Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Location."Consignment Ship-to Code INF" where(Code = field("Location Code")));
            ToolTip = 'Specifies the consignment ship-to code associated with this location.';
        }
    }

    keys
    {
        // Add keys if needed
    }
}