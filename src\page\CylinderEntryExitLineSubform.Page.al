page 60017 "CylinderEntryExitLineSubform"
{
    PageType = ListPart;
    SourceTable = "Cylinder Entry/Exit Line GAS";
    Caption = 'Cylinder Entry/Exit Lines';
    ApplicationArea = All;
    Extensible = false;

    layout
    {
        area(Content)
        {
            repeater(Group)
            {
                field("Line No."; Rec."Line No.")
                {
                    Caption = 'Line No.';
                }
                field("Entry/Exit Type"; Rec."Entry/Exit Type")
                {
                    Caption = 'Entry/Exit Type';
                }
                field("Cylinder Serial No."; Rec."Cylinder Serial No.")
                {
                    Caption = 'Cylinder Serial No.';
                }
                field("Cylinder Item No."; Rec."Cylinder Item No.")
                {
                    Caption = 'Cylinder Item No.';
                    trigger OnDrillDown()
                    var
                        ItemRec: Record "Item";
                    begin
                        if Rec."Cylinder Item No." <> '' then begin
                            ItemRec.SetRange("No.", Rec."Cylinder Item No.");
                            if ItemRec.FindFirst() then
                                PAGE.Run(PAGE::"Item Card", ItemRec);
                        end;
                    end;
                }
                field("Cylinder Item Description"; Rec."Cylinder Item Description")
                {
                    Caption = 'Cylinder Item Description';
                }
                field("Gas Item No."; Rec."Gas Item No.")
                {
                    Caption = 'Gas Item No.';
                    trigger OnDrillDown()
                    var
                        GasItemRec: Record "Item";
                    begin
                        if Rec."Gas Item No." <> '' then begin
                            GasItemRec.SetRange("No.", Rec."Gas Item No.");
                            if GasItemRec.FindFirst() then
                                PAGE.Run(PAGE::"Item Card", GasItemRec);
                        end;
                    end;
                }
                field("Gas Item Description"; Rec."Gas Item Description")
                {
                    Caption = 'Gas Item Description';
                }
                field("Gas Quantity"; Rec."Gas Quantity")
                {
                    Caption = 'Gas Quantity';
                }
                field("Read Cylinder Barcode"; Rec."Read Cylinder Barcode")
                {
                    Caption = 'Read Cylinder Barcode';
                }
            }
        }
    }

    actions
    {
        area(Processing)
        {
            action(ViewPostedWhseShipment)
            {
                Caption = 'View Posted Whse. Shipment';
                ApplicationArea = All;
                Image = PostedShipment;
                Enabled = Rec."Posted Warehouse Shipment No." <> '';
                ToolTip = 'Opens the related posted warehouse shipment for the selected line.';
                trigger OnAction()
                var
                    PstdWhseShptHeader: Record "Posted Whse. Shipment Header";
                begin
                    if Rec."Posted Warehouse Shipment No." <> '' then begin
                        PstdWhseShptHeader.SetRange("No.", Rec."Posted Warehouse Shipment No.");
                        if PstdWhseShptHeader.FindFirst() then
                            PAGE.Run(PAGE::"Posted Whse. Shipment", PstdWhseShptHeader);
                    end;
                end;
            }
        }
    }
}